<?php

/*
* Copyright (C) Abre.io Inc.
* Process Canvas Grades CSV and insert into abre_grades table
*/

// Enable garbage collection for better memory management
gc_enable();

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../utils/functions.php';
require_once __DIR__ . '/../utils/logging.php';

use Google\Cloud\Storage\StorageClient;
use phpseclib\Net\SFTP;

/**
 * Convert percentage to letter grade
 * Note: Very low scores (0-5) likely indicate missing/placeholder data, not actual grades
 */
function percentageToLetterGrade($percentage) {
    if (!is_numeric($percentage)) {
        return '';
    }

    $percentage = floatval($percentage);

    // Don't assign F for very low scores that might be placeholders/missing data
    if ($percentage <= 5) {
        return '';
    }
    if ($percentage >= 97) return 'A+';
    if ($percentage >= 93) return 'A';
    if ($percentage >= 90) return 'A-';
    if ($percentage >= 87) return 'B+';
    if ($percentage >= 83) return 'B';
    if ($percentage >= 80) return 'B-';
    if ($percentage >= 77) return 'C+';
    if ($percentage >= 73) return 'C';
    if ($percentage >= 70) return 'C-';
    if ($percentage >= 67) return 'D+';
    if ($percentage >= 63) return 'D';
    if ($percentage >= 60) return 'D-';
    return 'F';
}

/**
 * Helper function to extract core course name from various formats
 */
function extractCoreCourse($courseName) {
    $name = strtoupper(trim($courseName));
    
    // Remove SM codes (SM126-, etc.)
    $name = preg_replace('/^SM\d+[-\s]+/i', '', $name);
    
    // Remove teacher names after dash (e.g., -MARSHALL/VAUGHAN)
    $name = preg_replace('/\s*-\s*[A-Z][A-Z\s\/]+($|\s)/i', ' ', $name);
    
    // Extract text before parenthesis
    if (preg_match('/^([^(]+)/', $name, $matches)) {
        $name = trim($matches[1]);
    }
    
    // Remove trailing numbers that might be course codes
    $name = preg_replace('/\s+\d{3,}$/', '', $name);
    
    return trim($name);
}

/**
 * Check if course is elementary grade
 */
function isElementaryGrade($courseName) {
    $course = strtoupper(trim($courseName));
    return preg_match('/^(K|1ST|2ND|3RD|4TH|5TH|6TH)\s+GRADE/i', $course);
}

/**
 * Extract grade level from elementary course name
 */
function extractGradeLevel($courseName) {
    if (preg_match('/^(K|1ST|2ND|3RD|4TH|5TH|6TH)/i', $courseName, $matches)) {
        return strtoupper($matches[1]);
    }
    return null;
}

/**
 * Check if elementary subject is a core subject that should receive grades
 */
function isElementaryCoreSubject($className) {
    $coreSubjects = ['LANGUAGE ARTS', 'LA', 'MATH', 'SCIENCE', 'SCI', 'SOCIAL STUDIES', 'SS', 'HEALTH', 'HEA'];
    $class = strtoupper($className);
    
    foreach ($coreSubjects as $subject) {
        if (strpos($class, $subject) !== false) {
            return true;
        }
    }
    return false;
}

/**
 * Merge SFTP grades with Canvas API grades using improved matching logic
 */
function mergeGradesFiles($sftpGradesCSV, $apiGradesCSV = null)
{
    if (empty($apiGradesCSV)) {
        return $sftpGradesCSV;
    }

    $sftpLines = explode("\n", trim($sftpGradesCSV));
    $apiLines = explode("\n", trim($apiGradesCSV));

    if (empty($sftpLines) || empty($apiLines)) {
        return $sftpGradesCSV;
    }

    $sftpHeader = str_getcsv($sftpLines[0]);
    $apiHeader = str_getcsv($apiLines[0]);

    // Build Canvas grades indexed by student for efficient lookup
    $canvasGradesByStudent = [];
    $canvasRecords = 0;
    
    for ($i = 1; $i < count($apiLines); $i++) {
        if (trim($apiLines[$i]) === '') continue;

        $apiRow = str_getcsv($apiLines[$i]);
        if (count($apiRow) < 18) continue;

        $studentId = trim(trim($apiRow[0] ?? ''), '"');
        $courseName = trim($apiRow[3] ?? '');
        $teacherName = strtoupper(trim($apiRow[13] ?? ''));
        $staffId = trim($apiRow[14] ?? '');
        $sectionCode = trim($apiRow[5] ?? '');
        
        // Extract core course name for matching
        $coreCourse = extractCoreCourse($courseName);
        
        // Load ALL Canvas records for potential matching
        // We'll apply grades only if they exist, but we want to consider all records
        if (!empty($studentId)) {
            if (!isset($canvasGradesByStudent[$studentId])) {
                $canvasGradesByStudent[$studentId] = [];
            }
            
            $canvasGradesByStudent[$studentId][] = [
                'row' => $apiRow,
                'core_course' => $coreCourse,
                'teacher_name' => $teacherName,
                'staff_id' => $staffId,
                'section_code' => $sectionCode,
                'is_elementary' => isElementaryGrade($courseName),
                'grade_level' => extractGradeLevel($courseName)
            ];
            $canvasRecords++;
        }
    }
    
    error_log("Merge: Loaded {$canvasRecords} Canvas grade records from " . count($canvasGradesByStudent) . " students");

    // Start with SFTP header
    $mergedLines = [$sftpLines[0]];

    // Process SFTP data and merge with Canvas API data using improved matching
    $mergeCount = 0;
    $sftpRecordsProcessed = 0;
    $elementaryMerges = 0;
    
    for ($i = 1; $i < count($sftpLines); $i++) {
        if (trim($sftpLines[$i]) === '') continue;
        
        $sftpRow = str_getcsv($sftpLines[$i]);
        if (count($sftpRow) < 12) continue;

        $sftpRecordsProcessed++;
        $studentId = trim(trim($sftpRow[0] ?? ''), '"');
        $className = isset($sftpRow[7]) ? trim($sftpRow[7]) : '';
        $teacherName = isset($sftpRow[8]) ? strtoupper(trim($sftpRow[8])) : '';
        $staffId = isset($sftpRow[4]) ? trim($sftpRow[4]) : '';
        $sectionCode = isset($sftpRow[2]) ? trim($sftpRow[2]) : '';
        
        // Extract core course name for matching
        $sftpCoreCourse = extractCoreCourse($className);
        $isElementary = isElementaryGrade($className);
        $gradeLevel = extractGradeLevel($className);
        
        // Check if we have Canvas data for this student
        $gradeUpdated = false;
        
        if (isset($canvasGradesByStudent[$studentId])) {
            $bestMatch = null;
            $bestScore = 0;
            
            // Find best matching Canvas record for this SFTP record
            foreach ($canvasGradesByStudent[$studentId] as $canvasData) {
                $score = 0;
                
                if ($isElementary && $canvasData['is_elementary']) {
                    // Elementary grade matching
                    if ($gradeLevel === $canvasData['grade_level']) {
                        // Check if this is a core subject that should get the grade
                        if (isElementaryCoreSubject($className)) {
                            $score = 100; // Base score for grade level match
                            
                            // Teacher/staff matching
                            if (!empty($staffId) && $staffId === $canvasData['staff_id']) {
                                $score += 80;
                            } elseif (!empty($teacherName) && !empty($canvasData['teacher_name'])) {
                                similar_text($teacherName, $canvasData['teacher_name'], $similarity);
                                if ($similarity >= 90) {
                                    $score += 70;
                                } elseif ($similarity >= 70) {
                                    $score += 40;
                                }
                            }
                        }
                    }
                } else if (!$isElementary && !$canvasData['is_elementary']) {
                    // Regular course matching
                    similar_text($sftpCoreCourse, $canvasData['core_course'], $courseSimilarity);
                    
                    if ($courseSimilarity >= 90) {
                        $score = 150;
                    } elseif ($courseSimilarity >= 75) {
                        $score = 100;
                    } elseif ($courseSimilarity >= 60) {
                        $score = 50;
                    }
                    
                    // Teacher/staff matching
                    if (!empty($staffId) && $staffId === $canvasData['staff_id']) {
                        $score += 80;
                    } elseif (!empty($teacherName) && !empty($canvasData['teacher_name'])) {
                        similar_text($teacherName, $canvasData['teacher_name'], $similarity);
                        if ($similarity >= 90) {
                            $score += 70;
                        } elseif ($similarity >= 70) {
                            $score += 40;
                        }
                    }
                    
                    // Section code matching
                    if (!empty($sectionCode) && $sectionCode === $canvasData['section_code']) {
                        $score += 50;
                    }
                }
                
                if ($score > $bestScore) {
                    $bestScore = $score;
                    $bestMatch = $canvasData;
                }
            }
            
            // Apply grades if we have a good match (score >= 150 for regular, >= 100 for elementary with teacher match)
            if ($bestMatch && 
                ((!$isElementary && $bestScore >= 150) || 
                 ($isElementary && $bestScore >= 100))) {
                
                $apiRow = $bestMatch['row'];
                
                // Find grade fields in Canvas data
                $letterGradeIndex = 7;  // letter_grade
                $percentageIndex = 8;    // percentage
                $finalGradeIndex = 9;    // final_grade
                $finalScoreIndex = 10;   // final_score
                
                // Only update if SFTP doesn't already have grades
                if (empty($sftpRow[9]) && empty($sftpRow[10])) {
                    // Priority 1: Use letter_grade and percentage if available
                    if (!empty($apiRow[$letterGradeIndex])) {
                        $sftpRow[9] = $apiRow[$letterGradeIndex]; // Letter Grade column
                        $gradeUpdated = true;
                    }
                    
                    if (!empty($apiRow[$percentageIndex])) {
                        $score = $apiRow[$percentageIndex];
                        // Convert decimal to percentage if needed
                        if (is_numeric($score) && $score <= 1 && $score > 0) {
                            $score = $score * 100;
                        }
                        $sftpRow[10] = $score; // Percentage column
                        $gradeUpdated = true;
                    }
                    
                    // Priority 2: Use final_grade and final_score if no current grades
                    if (empty($sftpRow[9]) && !empty($apiRow[$finalGradeIndex])) {
                        $sftpRow[9] = $apiRow[$finalGradeIndex];
                        $gradeUpdated = true;
                    }
                    
                    if (empty($sftpRow[10]) && $apiRow[$finalScoreIndex] !== '') {
                        $score = $apiRow[$finalScoreIndex];
                        // Convert decimal to percentage if needed
                        if (is_numeric($score) && $score <= 1 && $score > 0) {
                            $score = $score * 100;
                        }
                        $sftpRow[10] = $score;
                        $gradeUpdated = true;
                    }
                    
                    // If we only have percentage but no letter grade, generate it
                    if ($gradeUpdated && empty($sftpRow[9]) && !empty($sftpRow[10])) {
                        $letterGrade = percentageToLetterGrade($sftpRow[10]);
                        if (!empty($letterGrade)) {
                            $sftpRow[9] = $letterGrade;
                        }
                    }
                    
                    if ($gradeUpdated) {
                        $mergeCount++;
                        if ($isElementary) {
                            $elementaryMerges++;
                        }
                        
                        // Debug first few matches
                        if ($mergeCount <= 5) {
                            error_log("✅ MATCHED: Student {$studentId}, {$className} with Canvas {$bestMatch['core_course']} (Score: {$bestScore})");
                        }
                    }
                }
            }
        }

        $mergedLines[] = implode(',', array_map(function($field) {
            return '"' . str_replace('"', '""', $field) . '"';
        }, $sftpRow));
    }

    // Summary statistics
    error_log("Merge: Processed {$sftpRecordsProcessed} SFTP records");
    error_log("Merge: Updated {$mergeCount} records with Canvas grades");
    if ($elementaryMerges > 0) {
        error_log("Merge: Including {$elementaryMerges} elementary grade records");
    }
    
    $matchRate = $canvasRecords > 0 ? round(($mergeCount / $canvasRecords) * 100, 1) : 0;
    error_log("📊 SUMMARY: {$mergeCount}/{$canvasRecords} Canvas records matched ({$matchRate}% match rate)");

    return implode("\n", $mergedLines);
}

/**
 * Insert Canvas grades from CSV content into abre_grades table
 * Based on Abre_Grades_2.0.php pattern
 *
 * @param mysqli $db Database connection
 * @param string $csvContent CSV file content
 * @param int $siteID Site identifier
 * @param int $schoolYearId School year identifier
 * @return bool True if successful, false otherwise
 */
function insertCanvasGradesFromCSV($db, $csvContent, $siteID, $schoolYearId)
{
    try {
        if (empty($csvContent)) {
            error_log('Cannot insert grades: CSV content is empty');
            return false;
        }

        define('SAFE_COLUMN_COUNT', 6);
        define('MAX_IMPORT_LIMIT', 25);

        $currentSchoolYearID = $schoolYearId;
        $rowCounter = 0;
        $valuesToImport = [];

        $dbColumns = "INSERT INTO abre_grades (
          student_id, course_code, section_code, school_code,
          staff_id, term_code, period, class_name, teacher_name, letter_grade,
          percentage, performance, site_id, school_year_id
         ) VALUES ";

        // Delete existing data for this site/year
        error_log("Database: Clearing existing grades for site {$siteID}, school year {$currentSchoolYearID}");
        $db->query("DELETE FROM abre_grades WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");

        $lines = explode("\n", trim($csvContent));
        $separator = "\n";

        // Skip header row and count total records
        $totalRecords = count($lines) - 1;
        error_log("Database: Processing {$totalRecords} grade records for insertion");
        $line = $lines[0]; // header
        for ($i = 1; $i < count($lines); $i++) {
            $line = $lines[$i];
            if (trim($line) === '') continue;

            $data = str_getcsv($line);

            if(count($data) >= SAFE_COLUMN_COUNT){
                $rowCounter++;

                $studentID = trim($db->escape_string($data[0]));
                $courseCode = trim($db->escape_string($data[1]));
                $sectionCode = trim($db->escape_string($data[2]));
                $schoolCode = trim($db->escape_string($data[3]));
                $staffID = trim($db->escape_string($data[4]));
                $termCode = trim($db->escape_string($data[5]));
                $period = isset($data[6]) ? trim($db->escape_string($data[6])) : '';
                $className = isset($data[7]) ? trim($db->escape_string($data[7])) : '';
                $teacherName = isset($data[8]) ? trim($db->escape_string($data[8])) : '';
                $letterGrade = isset($data[9]) ? trim($db->escape_string($data[9])) : '';
                $percentage = isset($data[10]) ? trim($db->escape_string($data[10])) : '';
                $performance = isset($data[11]) ? trim($db->escape_string($data[11])) : '';

                // Support for SIS's that can only map certain termcodes
                if($termCode == "S1"){ $termCode = "Sem1"; }
                if($termCode == "S2"){ $termCode = "Sem2"; }

                $valuesToImport []= "(
                  '$studentID', '$courseCode', '$sectionCode', '$schoolCode',
                  '$staffID', '$termCode', '$period', '$className', '$teacherName',
                  '$letterGrade', '$percentage', '$performance', $siteID, $currentSchoolYearID
                )";

                if(count($valuesToImport) == MAX_IMPORT_LIMIT){
                    insertRows($db, $dbColumns, $valuesToImport);
                    $valuesToImport = [];
                }
            }
        }

        // Insert remaining rows
        if(count($valuesToImport)){
            insertRows($db, $dbColumns, $valuesToImport);
        }

        error_log("Database: Successfully inserted {$rowCounter} grade records into abre_grades table");
        return $rowCounter;

    } catch (Exception $e) {
        error_log('Error inserting Canvas grades from CSV: ' . $e->getMessage());
        return 0;
    }
}


/**
 * Run job function for cron integration
 */
function runJob($db, $siteID, $config)
{
    $cronName = 'Process Canvas Grades';

    try {
        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        error_log("=== Process Canvas Grades from SFTP Started ===");
        error_log("Site ID: {$siteID}");

        // Get current school year
        $schoolYearId = getCurrentSchoolYearID($db);
        error_log("School Year ID: {$schoolYearId}");

        // Step 1: Get Abre_Grades_2.0.csv from SFTP
        $sftp = new SFTP($config->sftp->ip);
        if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
            throw new Exception("Login to SFTP failed.");
        }
        
        $sftpGradesFile = $sftp->get('Abre_Grades_2.0.csv');
        if (!$sftpGradesFile) {
            throw new Exception("Failed to download Abre_Grades_2.0.csv from SFTP");
        }
        error_log("Successfully downloaded Abre_Grades_2.0.csv from SFTP");
        
        // Step 2: Get 1-abre_grades_canvas_api.csv from GCS (the one with actual grades)
        $storage = new StorageClient(['projectId' => 'abre-production']);
        $bucketName = 'abre-canvas-skyward';
        $bucket = $storage->bucket($bucketName);
        
        $apiGradesPath = '1-abre_grades_canvas_api.csv';
        $object = $bucket->object($apiGradesPath);
        
        $apiGradesFile = null;
        if ($object->exists()) {
            $apiGradesFile = $object->downloadAsString();
            error_log("Successfully downloaded 1-abre_grades_canvas_api.csv from GCS");
        } else {
            error_log("Warning: 1-abre_grades_canvas_api.csv not found in GCS, proceeding with SFTP grades only");
        }
        
        // Step 3: Merge the files (API grades supplement/override SFTP grades)
        $mergedCSV = mergeGradesFiles($sftpGradesFile, $apiGradesFile);
        
        // Step 4: Insert merged grades into database
        $processedCount = 0;
        $errorCount = 0;
        
        $rowCount = insertCanvasGradesFromCSV($db, $mergedCSV, $siteID, $schoolYearId);

        if ($rowCount > 0) {
            $status = CRON_SUCCESS;
            $details = ["rowsInserted" => $rowCount];
        } else {
            $status = CRON_FAILURE;
            $details = ["error" => "Failed to insert grades"];
        }

        error_log("=== Process Canvas Grades from SFTP Complete ===");
        error_log("Processed: 1, Rows: {$rowCount}");

    } catch (Exception $ex) {
        $error = $ex->getMessage();
        error_log("Process Canvas Grades Error: " . $error);

        $status = CRON_FAILURE;
        $details = "Could not process Canvas grades from SFTP: " . $error;
    }

    // Log the cron job finish
    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
