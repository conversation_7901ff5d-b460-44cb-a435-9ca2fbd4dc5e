<?php

/*
* Copyright (C) Abre.io Inc.
*/

// Enable garbage collection for better memory management
gc_enable();

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../utils/functions.php';
require_once __DIR__ . '/../utils/logging.php';

use GuzzleHttp\Client;
use Google\Cloud\Storage\StorageClient;

/**
 * Upload data to Google Cloud Storage with organized folder structure
 *
 * GCS folder layout:
 * - site-id folder: /{date}/site-id/{siteID}/{filename}.json
 * - filename folder: /{date}/filename/{type}/{type}-{siteID}.json
 *
 * @param array $data Data to upload as JSON
 * @param string $type Type identifier for the data (e.g., 'grades', 'assignments')
 * @param object $bucket GCS bucket object
 * @param string $currentDate Date in Ymd format
 * @param int $siteID Site identifier
 */
function _uploadToGCS($data, $type, $bucket, $currentDate, $siteID)
{
    try {
        if (empty($data)) {
            error_log('No data to upload for type: ' . $type);
            return;
        }

        $jsonEncoded = json_encode($data);
        if ($jsonEncoded === false) {
            error_log('Failed to encode JSON for type: ' . $type);
            return;
        }

        // Upload to site-id folder
        $tempFile1 = tmpfile();
        fwrite($tempFile1, $jsonEncoded);
        rewind($tempFile1);
        $fileName = 'Abre_Canvas_Skyward_' . $type . '.json';
        $bucket->upload($tempFile1, [
            'name' => $currentDate . '/site-id/' . $siteID . '/' . $fileName
        ]);
        fclose($tempFile1);

        // Upload to filename folder
        $tempFile2 = tmpfile();
        fwrite($tempFile2, $jsonEncoded);
        rewind($tempFile2);
        $folderName = 'Abre_Canvas_Skyward_' . $type;
        $bucket->upload($tempFile2, [
            'name' => $currentDate . '/filename/' . $folderName . '/' . $folderName . '-' . $siteID . '.json'
        ]);
        fclose($tempFile2);

        error_log('Successfully uploaded ' . $type . ' to GCS');
    } catch (Exception $e) {
        error_log('Error uploading ' . $type . ' to GCS: ' . $e->getMessage());
    }
}

/**
 * Download CSV file from Google Cloud Storage
 *
 * @param object $bucket GCS bucket object
 * @param string $filePath Path to the file in GCS
 * @return string|null CSV content or null if download fails
 */
function _downloadCSVFromGCS($bucket, $filePath)
{
    try {
        $object = $bucket->object($filePath);
        if ($object->exists()) {
            $content = $object->downloadAsString();
            error_log("Successfully downloaded CSV from GCS: {$filePath}");
            return $content;
        } else {
            error_log("CSV file not found in GCS: {$filePath}");
            return null;
        }
    } catch (Exception $e) {
        error_log('Error downloading CSV from GCS: ' . $e->getMessage());
        return null;
    }
}

/**
 * Merge new Canvas grades with original Abre grades CSV
 *
 * @param string $newGradesCSV New Canvas grades CSV content
 * @param string $originalGradesCSV Original Abre grades CSV content
 * @return string|null Merged CSV content or null if merge fails
 */
function _mergeGradesCSV($newGradesCSV, $originalGradesCSV)
{
    try {
        if (empty($newGradesCSV) || empty($originalGradesCSV)) {
            error_log('Cannot merge CSVs: one or both files are empty');
            return null;
        }

        // Parse original CSV
        $originalLines = explode("\n", trim($originalGradesCSV));
        $originalHeader = str_getcsv($originalLines[0]);
        $originalData = [];
        
        for ($i = 1; $i < count($originalLines); $i++) {
            if (trim($originalLines[$i]) !== '') {
                $originalData[] = str_getcsv($originalLines[$i]);
            }
        }

        // Parse new Canvas grades CSV
        $newLines = explode("\n", trim($newGradesCSV));
        $newHeader = str_getcsv($newLines[0]);
        $newData = [];
        
        for ($i = 1; $i < count($newLines); $i++) {
            if (trim($newLines[$i]) !== '') {
                $newData[] = str_getcsv($newLines[$i]);
            }
        }

        // Create lookup map for new grades by student_id, course_id, assignment_id
        $newGradesMap = [];
        foreach ($newData as $newRow) {
            if (count($newRow) >= 4) {
                $key = $newRow[0] . '|' . $newRow[1] . '|' . $newRow[2];
                $newGradesMap[$key] = $newRow;
            }
        }

        // Find column indices for grade fields
        $newLetterGradeIndex = array_search('letter_grade', $newHeader);
        $newPercentageIndex = array_search('percentage', $newHeader);
        $originalLetterGradeIndex = array_search('letter_grade', $originalHeader);
        $originalPercentageIndex = array_search('percentage', $originalHeader);
        
        // If letter_grade and percentage don't exist in original, we'll need to add them
        $needsLetterGrade = ($originalLetterGradeIndex === false);
        $needsPercentage = ($originalPercentageIndex === false);
        
        // Merge data - update original with new grades where matches exist
        $mergedData = [];
        foreach ($originalData as $originalRow) {
            if (count($originalRow) >= 4) {
                $key = $originalRow[0] . '|' . $originalRow[1] . '|' . $originalRow[2];
                
                if (isset($newGradesMap[$key])) {
                    // Update with new grade data, preserving original structure
                    $newRow = $newGradesMap[$key];
                    $mergedRow = $originalRow;
                    
                    // Map letter_grade and percentage from new data to original data
                    if ($newLetterGradeIndex !== false && isset($newRow[$newLetterGradeIndex])) {
                        if ($originalLetterGradeIndex !== false) {
                            // Update existing letter_grade column
                            $mergedRow[$originalLetterGradeIndex] = $newRow[$newLetterGradeIndex];
                        } else {
                            // Add letter_grade column (will be handled in header update)
                            $mergedRow[] = $newRow[$newLetterGradeIndex];
                        }
                    }
                    
                    if ($newPercentageIndex !== false && isset($newRow[$newPercentageIndex])) {
                        if ($originalPercentageIndex !== false) {
                            // Update existing percentage column
                            $mergedRow[$originalPercentageIndex] = $newRow[$newPercentageIndex];
                        } else {
                            // Add percentage column (will be handled in header update)
                            $mergedRow[] = $newRow[$newPercentageIndex];
                        }
                    }
                    
                    $mergedData[] = $mergedRow;
                    unset($newGradesMap[$key]);
                } else {
                    // Keep original row unchanged
                    $mergedData[] = $originalRow;
                }
            }
        }

        // Add any new grades that didn't have matches in original data
        foreach ($newGradesMap as $newRow) {
            $mergedData[] = $newRow;
        }

        // Update header if needed to include letter_grade and percentage columns
        $finalHeader = $originalHeader;
        if ($needsLetterGrade) {
            $finalHeader[] = 'letter_grade';
        }
        if ($needsPercentage) {
            $finalHeader[] = 'percentage';
        }
        
        // Rebuild CSV content
        $mergedCSV = implode(',', $finalHeader) . "\n";
        foreach ($mergedData as $row) {
            // Ensure all rows have the same number of columns as the header
            while (count($row) < count($finalHeader)) {
                $row[] = '';
            }
            $mergedCSV .= implode(',', $row) . "\n";
        }

        error_log('Successfully merged grades CSV: ' . count($mergedData) . ' total records');
        return $mergedCSV;

    } catch (Exception $e) {
        error_log('Error merging grades CSV: ' . $e->getMessage());
        return null;
    }
}

/**
 * Insert grades from CSV into abre_grades table
 *
 * @param object $db Database connection
 * @param string $csvContent CSV content to insert
 * @param int $siteID Site identifier
 * @return bool True if successful, false otherwise
 */
function _insertGradesFromCSV($db, $csvContent, $siteID, $schoolYearId)
{
    try {
        if (empty($csvContent)) {
            error_log('Cannot insert grades: CSV content is empty');
            return false;
        }

        $lines = explode("\n", trim($csvContent));
        if (count($lines) < 2) {
            error_log('Cannot insert grades: CSV has no data rows');
            return false;
        }

        $header = str_getcsv($lines[0]);
        $insertedCount = 0;
        $errorCount = 0;

        // Prepare insert statement
        $insertSQL = "INSERT INTO abre_grades (student_id, course_code, section_code, school_code, staff_id, term_code, period, class_name, teacher_name, letter_grade, percentage, performance, site_id, school_year_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($insertSQL);

        if (!$stmt) {
            error_log('Failed to prepare insert statement: ' . $db->error);
            return false;
        }

        // Find column indices for database fields
        $studentIdIndex = array_search('Student ID', $header);
        $courseCodeIndex = array_search('Course Code', $header);
        $sectionCodeIndex = array_search('Section Code', $header);
        $schoolCodeIndex = array_search('School Code', $header);
        $staffIdIndex = array_search('Staff ID', $header);
        $termCodeIndex = array_search('Term Code', $header);
        $periodIndex = array_search('Period', $header);
        $classNameIndex = array_search('Class Name', $header);
        $teacherNameIndex = array_search('Teacher Name', $header);
        $letterGradeIndex = array_search('Letter Grade', $header);
        $percentageIndex = array_search('Percentage', $header);
        $performanceIndex = array_search('Performance', $header);

        // Process data rows
        for ($i = 1; $i < count($lines); $i++) {
            if (trim($lines[$i]) === '') {
                continue;
            }

            $row = str_getcsv($lines[$i]);
            if (count($row) < 5) {
                $errorCount++;
                continue;
            }

            // Bind parameters using column indices
            $studentId = ($studentIdIndex !== false) ? ($row[$studentIdIndex] ?? null) : null;
            $courseCode = ($courseCodeIndex !== false) ? ($row[$courseCodeIndex] ?? null) : null;
            $sectionCode = ($sectionCodeIndex !== false) ? ($row[$sectionCodeIndex] ?? null) : null;
            $schoolCode = ($schoolCodeIndex !== false) ? ($row[$schoolCodeIndex] ?? null) : null;
            $staffId = ($staffIdIndex !== false) ? ($row[$staffIdIndex] ?? null) : null;
            $termCode = ($termCodeIndex !== false) ? ($row[$termCodeIndex] ?? null) : null;
            $period = ($periodIndex !== false) ? ($row[$periodIndex] ?? null) : null;
            $className = ($classNameIndex !== false) ? ($row[$classNameIndex] ?? null) : null;
            $teacherName = ($teacherNameIndex !== false) ? ($row[$teacherNameIndex] ?? null) : null;
            $letterGrade = ($letterGradeIndex !== false) ? ($row[$letterGradeIndex] ?? null) : null;
            $percentage = ($percentageIndex !== false) ? ($row[$percentageIndex] ?? null) : null;
            $performance = ($performanceIndex !== false) ? ($row[$performanceIndex] ?? null) : null;

            $stmt->bind_param('ssssssssssssii', 
                $studentId, 
                $courseCode, 
                $sectionCode, 
                $schoolCode, 
                $staffId, 
                $termCode, 
                $period, 
                $className, 
                $teacherName, 
                $letterGrade, 
                $percentage, 
                $performance, 
                $siteID,
                $schoolYearId
            );

            if ($stmt->execute()) {
                $insertedCount++;
            } else {
                $errorCount++;
                error_log('Failed to insert grade record: ' . $stmt->error);
            }
        }

        $stmt->close();

        error_log("Grades insertion complete: {$insertedCount} inserted, {$errorCount} errors");
        return $insertedCount > 0;

    } catch (Exception $e) {
        error_log('Error inserting grades from CSV: ' . $e->getMessage());
        return false;
    }
}

/**
 * Upload merged CSV content to Google Cloud Storage as final version
 *
 * @param string $csvContent CSV content to upload
 * @param object $bucket GCS bucket object
 * @param string $currentDate Date in Ymd format
 * @param int $siteID Site identifier
 * @return bool True if successful, false otherwise
 */
function _uploadFinalGradesCSVToGCS($csvContent, $bucket, $currentDate, $siteID)
{
    try {
        if (empty($csvContent)) {
            error_log('No CSV content to upload as final version');
            return false;
        }

        // Create temporary file for CSV data
        $tempFile = tmpfile();
        fwrite($tempFile, $csvContent);
        rewind($tempFile);

        // Upload to main bucket folder (no nested structure)
        $mainPath = "abre_grades_canvas_api.csv";
        $bucket->upload($tempFile, [
            'name'     => $mainPath,
            'metadata' => [
                'contentType' => 'text/csv',
                'siteId'      => $siteID,
                'uploadDate'  => $currentDate,
                'version'     => 'final'
            ]
        ]);

        fclose($tempFile);

        error_log('Successfully uploaded final grades CSV to GCS');
        return true;

    } catch (Exception $e) {
        error_log('Error uploading final grades CSV to GCS: ' . $e->getMessage());
        return false;
    }
}

/**
 * Upload grades data as CSV to Google Cloud Storage
 *
 * @param array $gradesData Array of grade records
 * @param object $bucket GCS bucket object
 * @param string $currentDate Date in Ymd format
 * @param int $siteID Site identifier
 */
function _uploadGradesCSVToGCS($gradesData, $bucket, $currentDate, $siteID)
{
    try {
        if (empty($gradesData)) {
            error_log('No grades data to upload to GCS');
            return;
        }

        // Create CSV content
        $csvContent = '';
        
        // Add CSV header
        $headers = [
            'student_id',
            'canvas_student_id', 
            'course_id',
            'course_name',
            'section_id',
            'section_code',
            'section_name',
            'letter_grade',
            'percentage',
            'final_grade',
            'final_score',
            'enrollment_state',
            'last_activity_at',
            'teacher_name',
            'staff_id',
            'term_code',
            'school_code',
            'period'
        ];
        $csvContent .= implode(',', $headers) . "\n";

        // Add data rows
        foreach ($gradesData as $record) {
            $row = [
                $record['student_id'] ?? '',
                $record['canvas_student_id'] ?? '',
                $record['course_id'] ?? '',
                '"' . str_replace('"', '""', $record['course_name'] ?? '') . '"',
                $record['section_id'] ?? '',
                $record['section_code'] ?? '',
                '"' . str_replace('"', '""', $record['section_name'] ?? '') . '"',
                $record['current_grade'] ?? '',
                $record['current_score'] ?? '',
                $record['final_grade'] ?? '',
                $record['final_score'] ?? '',
                $record['enrollment_state'] ?? '',
                $record['last_activity_at'] ?? '',
                '"' . str_replace('"', '""', $record['teacher_name'] ?? '') . '"',
                $record['staff_id'] ?? '',
                $record['term_code'] ?? '',
                $record['school_code'] ?? '',
                $record['period'] ?? ''
            ];
            $csvContent .= implode(',', $row) . "\n";
        }

        // Create temporary file for CSV data
        $tempFile = tmpfile();
        fwrite($tempFile, $csvContent);
        rewind($tempFile);

        // Upload to site-id folder structure
        $siteIdPath = "abre_grades_canvas_api.csv";
        $bucket->upload($tempFile, [
            'name'     => $siteIdPath,
            'metadata' => [
                'contentType' => 'text/csv',
                'siteId'      => $siteID,
                'uploadDate'  => $currentDate
            ]
        ]);
        fclose($tempFile);

        // Create temporary file for filename folder structure
        $tempFile2 = tmpfile();
        fwrite($tempFile2, $csvContent);
        rewind($tempFile2);

        // Upload to filename folder structure
        $filenamePath = "1-abre_grades_canvas_api.csv";
        $bucket->upload($tempFile2, [
            'name'     => $filenamePath,
            'metadata' => [
                'contentType' => 'text/csv',
                'siteId'      => $siteID,
                'uploadDate'  => $currentDate
            ]
        ]);
        fclose($tempFile2);

        error_log('Successfully uploaded grades CSV to GCS: ' . count($gradesData) . ' records');
    } catch (Exception $e) {
        error_log('Error uploading grades CSV to GCS: ' . $e->getMessage());
    }
}

/**
 * Canvas API Client for Canvas SIS Integration (Skyward data)
 * Handles OAuth2 authentication, API requests, and data processing
 */
class CanvasApiClient
{
    private $baseUrl;
    private $clientId;
    private $clientSecret;
    private $accessToken;
    private $refreshToken;
    private $debugMode;
    private $client;
    private $tokenExpiryTime;

    public function __construct($baseUrl, $clientId, $clientSecret, $refreshToken = null, $debugMode = false)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->refreshToken = $refreshToken;
        $this->debugMode = $debugMode;

        // Initialize Guzzle client
        $this->initializeClient();
    }

    /**
     * Initialize or reinitialize Guzzle HTTP client
     */
    private function initializeClient()
    {
        $this->client = new Client([
            'base_uri'        => $this->baseUrl,
            'timeout'         => 600,
            'connect_timeout' => 30,
            'read_timeout'    => 600,
            'http_errors'     => false,
            'verify'          => true,
            'curl'            => [
                CURLOPT_MAXREDIRS     => 3,
                CURLOPT_FORBID_REUSE  => false,
                CURLOPT_FRESH_CONNECT => false
            ]
        ]);
    }

    /**
     * Check if token needs proactive refresh and refresh if needed
     */
    private function checkTokenExpiry()
    {
        if (isset($this->tokenExpiryTime) && time() >= $this->tokenExpiryTime) {
            // Refreshing token
            $this->authenticate();
        }
    }

    /**
     * Smart rate limiting - only delay if we're making requests too quickly
     */
    private function enforceRateLimit()
    {
        static $requestCount = 0;
        static $lastRequestTime = 0;

        $requestCount++;
        $currentTime = microtime(true);

        // Only enforce rate limiting if we're making many requests quickly
        if ($requestCount % 100 === 0) {
            $timeSinceLastCheck = $currentTime - $lastRequestTime;
            if ($timeSinceLastCheck < 10) {
                usleep(50000);
            }
            $lastRequestTime = $currentTime;
        }
    }

    /**
     * Authenticate with Canvas API using OAuth2
     *
     * @return bool True if authentication successful, false otherwise
     * @throws Exception When authentication fails
     */
    public function authenticate()
    {
        try {
            if ($this->debugMode) {
                // Authenticating with Canvas API
            }

            // Use refresh token if available
            // Early return if no refresh token
            if (!$this->refreshToken) {
                // No refresh token available
                return false;
            }

            // Attempting token refresh

            $response = $this->client->request('POST', '/login/oauth2/token', [
                'form_params' => [
                    'grant_type'    => 'refresh_token',
                    'refresh_token' => $this->refreshToken,
                    'client_id'     => $this->clientId,
                    'client_secret' => $this->clientSecret
                ],
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ]
            ]);

            // Token refresh response received

            // Early return if response not successful
            if ($response->getStatusCode() !== 200) {
                error_log("Canvas API: Refresh failed with status: " . $response->getStatusCode());
                return false;
            }

            $tokenData = json_decode($response->getBody(), true);

            // Early return if invalid token response
            if (!$tokenData || !isset($tokenData['access_token'])) {
                error_log("Canvas API: Invalid token response format");
                return false;
            }

            // Happy path: Update tokens and reinitialize client
            $this->accessToken = $tokenData['access_token'];
            // Set token expiry time for proactive refresh (55 minutes from now)
            $this->tokenExpiryTime = time() + (50 * 60);

            // Update refresh token if provided (some OAuth implementations send new refresh tokens)
            if (isset($tokenData['refresh_token'])) {
                $this->refreshToken = $tokenData['refresh_token'];
                // Updated refresh token received
            }

            // Reinitialize Guzzle client with new token
            $this->initializeClient();

            // New access token obtained
            return true;

        } catch (Exception $e) {
            error_log("Canvas API Authentication Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Set access token manually (alternative to OAuth)
     *
     * @param string $token Access token to set
     */
    public function setAccessToken($token)
    {
        $this->accessToken = $token;
        if ($this->debugMode) {
            // Manual access token set
        }
    }

    /**
     * Make GET request to Canvas API with retry logic
     *
     * @param string $endpoint API endpoint to call
     * @param array $params Query parameters
     * @param bool $singlePage If true, return a single page without pagination
     * @return array|false API response data or false on failure
     * @throws Exception When API request fails after max retries
     */
    public function get($endpoint, $params = [], $singlePage = false)
    {
        $maxRetries = 3;
        $retryCount = 0;
        $authRetryCount = 0;
        $maxAuthRetries = 2;
        $totalAttempts = 0;
        $maxTotalAttempts = 10;

        // Initialize pagination variables outside the retry loop
        $allData = [];
        $currentUrl = $endpoint;
        $currentParams = $params;
        $originalPerPage = $params['per_page'] ?? null;
        $pageCount = 0;
        $maxPages = 10000;
        $visitedNextUrls = [];
        $isTermsEndpoint = (strpos($endpoint, '/terms') !== false);
        $isCoursesEndpoint = (strpos($endpoint, '/courses') !== false);
        $prevTermsCount = null;
        $seenIds = [];

        while ($retryCount < $maxRetries && $totalAttempts < $maxTotalAttempts) {
            $totalAttempts++;

            // Proactively check and refresh token if needed
            $this->checkTokenExpiry();

            // Smart rate limiting
            $this->enforceRateLimit();

            try {
                if ($this->debugMode) {
                    // Canvas API call
                }

                do {
                    $pageCount++;
                    if ($pageCount > $maxPages) {
                        error_log("Canvas API: Pagination limit exceeded");
                        break;
                    }

                    // Check if we have a valid access token
                    if (empty($this->accessToken)) {
                        throw new Exception("No access token available");
                    }

                    // Set headers dynamically for each request
                    $requestOptions = [
                        'headers' => [
                            'Authorization' => 'Bearer ' . $this->accessToken,
                            'Content-Type'  => 'application/json'
                        ]
                    ];

                    // If Link provided an absolute URL, call it verbatim
                    if (strpos($currentUrl, 'http') === 0) {
                        $response = $this->client->request('GET', $currentUrl, $requestOptions);
                    } else {
                        $requestOptions['query'] = $currentParams;
                        $response = $this->client->request('GET', $currentUrl, $requestOptions);
                    }

                    if ($response->getStatusCode() != 200) {
                        throw new Exception("API request failed with status: " . $response->getStatusCode());
                    }

                    $data = json_decode($response->getBody(), true);
                    $stopPaginationNow = false;

                    // Merge list vs wrapper
                    if (is_array($data)) {
                        $isList = empty($data) ? true : array_keys($data) === range(0, count($data) - 1);
                        if ($isList) {
                            // Check for no new data (applies to courses, sections, enrollments, etc.)
                            if ($isCoursesEndpoint || !$isTermsEndpoint) {
                                $pageDataCount = count($data);
                                if ($pageDataCount === 0) {
                                    if ($this->debugMode) {
                                        // Empty page, stopping pagination
                                    }
                                    $stopPaginationNow = true;
                                }

                                // Check for duplicate IDs to detect repeated data
                                $newIds = 0;
                                foreach ($data as $item) {
                                    if (isset($item['id'])) {
                                        $id = $item['id'];
                                        if (!isset($seenIds[$id])) {
                                            $seenIds[$id] = true;
                                            $newIds++;
                                        }
                                    }
                                }

                                if ($pageDataCount > 0 && $newIds === 0) {
                                    if ($this->debugMode) {
                                        error_log('Canvas API: all items on page already seen; stopping pagination');
                                    }
                                    $stopPaginationNow = true;
                                } else if ($this->debugMode && $pageCount % 10 === 0) {
                                    error_log("Canvas API: page {$pageCount}, {$newIds} new items, {$pageDataCount} total items on page");
                                }
                            }
                            $allData = array_merge($allData, $data);
                        } else {
                            if (isset($data['enrollment_terms']) && is_array($data['enrollment_terms'])) {
                                if ($isTermsEndpoint) {
                                    $pageTermsCount = count($data['enrollment_terms']);
                                    if ($pageTermsCount === 0) {
                                        if ($this->debugMode) {
                                            error_log('Canvas terms: empty page; stopping pagination');
                                        }
                                        $stopPaginationNow = true;
                                    }
                                }
                                if (!isset($allData['enrollment_terms'])) {
                                    $allData['enrollment_terms'] = [];
                                }
                                $allData['enrollment_terms'] = array_merge($allData['enrollment_terms'], $data['enrollment_terms']);
                                if ($this->debugMode) {
                                    error_log("Canvas terms merged count: " . count($allData['enrollment_terms']));
                                }
                                if ($isTermsEndpoint) {
                                    $currentTermsCount = count($allData['enrollment_terms']);
                                    if ($prevTermsCount !== null && $currentTermsCount <= $prevTermsCount) {
                                        if ($this->debugMode) {
                                            error_log('Canvas terms: no new terms detected; stopping pagination');
                                        }
                                        $stopPaginationNow = true;
                                    }
                                    $prevTermsCount = $currentTermsCount;
                                }
                            } else {
                                $allData = array_merge($allData, $data);
                            }
                        }
                    }

                    // Skip pagination if single page requested
                    if ($singlePage) {
                        break;
                    }

                    // Pagination via Link header - preserve per_page parameter
                    $linkHeader = $response->getHeader('Link');
                    $nextUrl = null;
                    if ($stopPaginationNow) {
                        // Explicitly stop pagination when no progress is detected (e.g., buggy Link headers)
                        $linkHeader = [];
                    }
                    if (!empty($linkHeader)) {
                        $links = explode(',', $linkHeader[0]);
                        foreach ($links as $link) {
                            if (strpos($link, 'rel="next"') !== false) {
                                preg_match('/<([^>]+)>/', $link, $matches);
                                if (isset($matches[1])) {
                                    $fullNext = $matches[1];

                                    // If we have a per_page parameter, modify the URL to use it
                                    if ($originalPerPage && strpos($fullNext, 'per_page=') === false) {
                                        $separator = strpos($fullNext, '?') !== false ? '&' : '?';
                                        $fullNext .= $separator . 'per_page=' . $originalPerPage;
                                    }
                                    // Loop protection: stop if we've already seen this URL
                                    if (isset($visitedNextUrls[$fullNext])) {
                                        if ($this->debugMode) {
                                            error_log("Canvas pagination: detected loop on {$fullNext}, stopping");
                                        }
                                        $nextUrl = null;
                                        break;
                                    }
                                    $visitedNextUrls[$fullNext] = true;
                                    $currentUrl = $fullNext;
                                    $currentParams = [];
                                    $nextUrl = $fullNext;
                                    if ($this->debugMode) {
                                        error_log("Canvas pagination: page {$pageCount} => {$currentUrl}");
                                    }
                                }
                                break;
                            }
                        }
                    }
                } while ($nextUrl);

                if ($pageCount > 1 && $this->debugMode) {
                    error_log("Canvas API: Completed pagination through {$pageCount} pages, total records: " . count($allData));
                }

                return $allData;

            } catch (Exception $e) {
                $retryCount++;
                error_log("Canvas API Error (attempt {$retryCount}): " . $e->getMessage());

                // Handle 401 Unauthorized - distinguish between token expiration and insufficient scopes
                if (strpos($e->getMessage(), '401') !== false && $authRetryCount < $maxAuthRetries) {
                    // Check if it's a scope issue vs token expiration
                    if (strpos($e->getMessage(), 'Insufficient scopes') !== false) {
                        error_log("Canvas API: Insufficient scopes detected for {$endpoint} - returning false (expected for sub_accounts)");
                        // Don't retry scope errors - return false so caller can handle gracefully
                        return false;
                    } else if (strpos($e->getMessage(), 'Invalid access token') !== false ||
                               strpos($e->getMessage(), '401') !== false) {
                        $authRetryCount++;
                        error_log("Canvas API: Token auth error detected (attempt {$authRetryCount}/{$maxAuthRetries}), attempting refresh");

                        if ($this->authenticate()) {
                            error_log("Canvas API: Token refreshed successfully, retrying current request immediately");
                            // Don't reset pagination state - just retry the current request
                            $retryCount = 0;
                            // Don't continue the outer loop - just retry the current pagination request
                            break;
                        } else {
                            error_log("Canvas API: Token refresh failed (attempt {$authRetryCount}/{$maxAuthRetries})");
                            // If token refresh fails, fall through to regular retry logic
                        }
                    }
                }

                if ($retryCount >= $maxRetries) {
                    error_log("Canvas API: Max retries exceeded for {$endpoint}");
                    return false;
                }
                if ($totalAttempts >= $maxTotalAttempts) {
                    error_log("Canvas API: Max total attempts exceeded for {$endpoint} - preventing infinite loop");
                    return false;
                }
                sleep(pow(2, $retryCount));
            }
        }

        return false;
    }

    /**
     * Make GET request to Canvas API for a single page (no pagination)
     * Useful when you only need a limited number of records
     *
     * @param string $endpoint API endpoint to call
     * @param array $params Query parameters
     * @return array|false API response data or false on failure
     * @throws Exception When API request fails after max retries
     */
    public function getSinglePage($endpoint, $params = [])
    {
        return $this->get($endpoint, $params, true);
    }
}

/**
 * Canvas Grades Processor - handles Canvas API sequence for grades with Skyward-SIS integration
 */
class CanvasGradesProcessor
{
    // Class constants for configuration values
    const MAX_COURSES_TO_PROCESS = 250000;
    const ASSIGNMENT_BATCH_SIZE = 50000;
    const GCS_UPLOAD_BATCH_SIZE = 50000;
    const MAX_RETRIES = 3;
    const PER_PAGE_LIMIT = 10000;
    const MEMORY_CLEANUP_INTERVAL = 10;

    private $canvasApi;
    private $db;
    private $siteId;
    private $schoolYearId;
    private $debugMode;
    private $canvasTermIds;
    private $gradesData = [];
    private $assignmentsData = [];
    private $assignmentsDataForGcs = [];
    private $jobUuid;
    private $termCache = [];
    private $_assignmentsBatchingStarted = false;
    private $sectionTeacherCache = [];
    private $enrollmentCache = [];
    private $assignmentGroupsCache = [];
    private $apiCallCount = 0;
    private $lastApiCallTime = 0;
    private $accountId;

    public function __construct($canvasApi, $db, $siteId, $schoolYearId, $debugMode = false, $canvasTermIds = [], $jobUuid = null)
    {
        $this->canvasApi = $canvasApi;
        $this->db = $db;
        $this->siteId = $siteId;
        $this->schoolYearId = $schoolYearId;
        $this->debugMode = $debugMode;
        $this->canvasTermIds = $canvasTermIds;
        $this->jobUuid = $jobUuid;

        // Ensure arrays are properly initialized for production stability
        $this->assignmentsData = [];
        $this->gradesData = [];
        $this->assignmentsDataForGcs = [];
    }

    /**
     * Process Canvas grades
     *
     * @return int Number of processed grade records
     * @throws Exception When processing fails
     */
    public function processGrades()
    {
        try {
            $processedRecords = 0;

            // Get account information
            $accounts = $this->canvasApi->get('/api/v1/accounts');
            if ($accounts === false || empty($accounts)) {
                throw new Exception("Failed to retrieve Canvas accounts");
            }

            $accountId = $accounts[0]['id'];
            $this->accountId = $accountId;
            $this->logInfo("Using account ID: {$accountId}");

            // Clear existing assignments for this site/year once at the beginning
            $this->clearExistingAssignments();
            $this->clearExistingClassPosts();

            // Use DB-provided Canvas term IDs if present; otherwise fallback to API discovery
            if (!empty($this->canvasTermIds)) {
                $processedRecords = $this->processGradesWithDbTerms($accountId);
            } else {
                $processedRecords = $this->processGradesWithApiDiscovery($accountId);
            }

            return $processedRecords;

        } catch (Exception $e) {
            $this->logError("Grades Processing Error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process grades using database-provided term IDs
     *
     * @param int $accountId Canvas account ID
     * @return int Number of processed records
     */
    private function processGradesWithDbTerms($accountId)
    {
        $processedRecords = 0;
        $totalCourses = 0;

        // Build account + subaccount list
        $accountIds = $this->buildAccountIdList($accountId);

        foreach ($this->canvasTermIds as $termId) {
            $this->logInfo("Processing term id {$termId} from DB");
            $courses = $this->discoverCoursesForTerm($accountIds, $termId);

            if (!empty($courses)) {
                $totalCourses += count($courses);
                $processedRecords += $this->processCourseGrades($courses);
            }
        }

        $this->logInfo("Total courses across DB terms: {$totalCourses}");
        return $processedRecords;
    }

    /**
     * Process grades using API discovery of terms
     *
     * @param int $accountId Canvas account ID
     * @return int Number of processed records
     */
    private function processGradesWithApiDiscovery($accountId)
    {
        $termsResponse = $this->canvasApi->get("/api/v1/accounts/{$accountId}/terms");
        if ($termsResponse === false) {
            throw new Exception("Failed to retrieve Canvas terms");
        }

        $terms = $termsResponse['enrollment_terms'] ?? [];
        $this->logInfo("Found " . count($terms) . " terms");

        // Build account + subaccount list once
        $accountIds = $this->buildAccountIdList($accountId);
        $processedRecords = 0;
        $totalCourses = 0;

        // Process ALL terms that have course data
        foreach ($terms as $term) {
            if ($term['name'] === 'X MASS ARCHIVE') {
                continue;
            }

            $this->logInfo("Processing term: {$term['name']}");
            $courses = $this->discoverCoursesForTerm($accountIds, $term['id']);

            if (!empty($courses)) {
                $this->logInfo("Found " . count($courses) . " courses in term: {$term['name']}");
                $totalCourses += count($courses);
                $processedRecords += $this->processCourseGrades($courses);
            } else {
                $this->logInfo("No courses found in term: {$term['name']}");
            }
        }

        $this->logInfo("Total courses across all terms: {$totalCourses}");
        return $processedRecords;
    }

    /**
     * Build list of account IDs - just return main account since no sub-accounts
     *
     * @param int $accountId Main account ID
     * @return array List of account IDs
     */
    private function buildAccountIdList($accountId)
    {
        // No sub-accounts for Wayzata
        return [$accountId];
    }

    /**
     * Discover courses for a specific term
     *
     * @param array $accountIds List of account IDs to search (just main account)
     * @param int $termId Term ID to search for
     * @return array List of discovered courses
     */
    private function discoverCoursesForTerm($accountIds, $termId)
    {
        $courses = [];
        $seen = [];

        foreach ($accountIds as $accId) {
            // Pass 1: available courses - use full pagination for ALL courses
            $batchA = $this->canvasApi->get("/api/v1/accounts/{$accId}/courses", [
                'enrollment_term_id' => $termId,
                'per_page'           => self::PER_PAGE_LIMIT,
                'state[]'            => 'available'
            ]);
            if (is_array($batchA)) {
                foreach ($batchA as $c) {
                    if (!empty($c['id']) && !isset($seen[$c['id']])) {
                        $seen[$c['id']] = true;
                        $courses[] = $c;
                    }
                }
            }

            // Pass 2: completed courses - use full pagination for ALL courses
            $batchC = $this->canvasApi->get("/api/v1/accounts/{$accId}/courses", [
                'enrollment_term_id' => $termId,
                'per_page'           => self::PER_PAGE_LIMIT,
                'state[]'            => 'completed'
            ]);
            if (is_array($batchC)) {
                foreach ($batchC as $c) {
                    if (!empty($c['id']) && !isset($seen[$c['id']])) {
                        $seen[$c['id']] = true;
                        $courses[] = $c;
                    }
                }
            }
        }

        return $courses;
    }

    /**
     * Find active term with course data
     *
     * @param int $accountId Canvas account ID
     * @param array $terms List of available terms
     * @return array|null Term data or null if none found
     */
    private function findActiveTermWithData($accountId, $terms)
    {
        $currentTerm = null;
        $maxCourses = 0;

        // Test each term to find one with courses
        foreach ($terms as $term) {
            if ($term['name'] === 'X MASS ARCHIVE') {
                continue;
            }

            $testCourses = $this->canvasApi->get("/api/v1/accounts/{$accountId}/courses", [
                'enrollment_term_id' => $term['id'],
                'state[]'            => 'available',
                'per_page'           => 500
            ]);

            if ($testCourses !== false) {
                $courseCount = count($testCourses);
                if ($this->debugMode) {
                    $this->logInfo("Term '{$term['name']}' has {$courseCount} courses");
                }

                if ($courseCount > $maxCourses) {
                    $maxCourses = $courseCount;
                    $currentTerm = $term;
                }
            }
        }

        return $currentTerm;
    }

    /**
     * Process courses and extract grades with SIS integration
     *
     * @param array $courses List of courses to process
     * @return int Number of processed records
     */
    private function processCourseGrades($courses)
    {
        $processedRecords = 0;
        $totalCourses = count($courses);
        $coursesProcessed = 0;

        $this->logInfo("Processing {$totalCourses} courses");

        // Process courses in fast large chunks for maximum throughput
        $courseChunks = array_chunk($courses, 50);

        $startTime = microtime(true);
        $lastProgressTime = $startTime;

        foreach ($courseChunks as $chunkIndex => $courseChunk) {
            $chunkStartTime = microtime(true);
            $this->logInfo("Processing course chunk " . ($chunkIndex + 1) . "/" . count($courseChunks) . " (" . count($courseChunk) . " courses)");

            foreach ($courseChunk as $course) {
            if ($coursesProcessed >= self::MAX_COURSES_TO_PROCESS) {
                break;
            }

            $courseId = $course['id'];
            $courseName = $course['name'];

            // Skip template/sample courses
            if ($this->isTemplateCourse($courseName)) {
                continue;
            }

            $this->logInfo("Starting course {$courseId}: {$courseName} (course " . ($coursesProcessed + 1) . "/{$totalCourses})");

            // Process course sections and grades
            $processedRecords += $this->processCourseSections($course);

            // Collect assignments + submissions
            $this->collectAssignmentData($course);

            $coursesProcessed++;

            // Fast assignment processing - process after every 2 courses to prevent memory buildup
            if ($coursesProcessed % 2 === 0) {
                // Clear assignment data after every 2 courses to prevent memory explosion
                if (!empty($this->assignmentsData) && count($this->assignmentsData) > 0) {
                    $this->insertAssignmentsBatchFast($this->assignmentsData);
                }

                // Force memory cleanup every 5 courses
                if ($coursesProcessed % 5 === 0) {
                    $this->forceMemoryCleanup();
                    if ($this->debugMode) {
                        $this->logInfo("Memory cleanup after {$coursesProcessed} courses");
                    }
                }
            }
            // Fast progress logging
            if ($coursesProcessed % 2000 === 0) {
                $this->logInfo("Processed {$coursesProcessed}/{$totalCourses} courses...");
            }
            }

            // Log progress after each chunk with performance metrics
            $chunkEndTime = microtime(true);
            $chunkDuration = $chunkEndTime - $chunkStartTime;
            $totalDuration = $chunkEndTime - $startTime;
            $avgTimePerCourse = $totalDuration / max(1, $coursesProcessed);
            $estimatedTimeRemaining = ($totalCourses - $coursesProcessed) * $avgTimePerCourse;

            $memoryUsage = memory_get_usage(true) / 1024 / 1024;
            $this->logInfo(sprintf(
                "Completed course chunk %d/%d. Total courses: %d/%d. Chunk time: %.2fs. Avg/course: %.2fs. Est remaining: %.2fm. Memory: %.1f MB",
                $chunkIndex + 1,
                count($courseChunks),
                $coursesProcessed,
                $totalCourses,
                $chunkDuration,
                $avgTimePerCourse,
                $estimatedTimeRemaining / 60,
                $memoryUsage
            ));

            // Force aggressive memory cleanup between chunks
            $this->forceMemoryCleanup();
        }

        // Process any remaining assignments that weren't processed in chunks
        $assignmentCount = 0;
        if (!empty($this->assignmentsData)) {
            if ($this->debugMode) {
                $this->logInfo("Processing final " . count($this->assignmentsData) . " assignment records");
            }
            $assignmentCount += $this->insertAssignmentsBatchFast($this->assignmentsData);
            $this->assignmentsData = [];
            $this->assignmentsDataForGcs = [];
        }

        $endTime = microtime(true);
        $totalDuration = $endTime - $startTime;
        $avgTimePerCourse = $totalDuration / max(1, $totalCourses);

        $finalMemory = memory_get_usage(true) / 1024 / 1024;
        $peakMemory = memory_get_peak_usage(true) / 1024 / 1024;

        $this->logInfo(sprintf(
            "PERFORMANCE SUMMARY: Processed %d courses in %.2fs (avg: %.2fs/course). Records: %d grades, %d assignments. Memory: %.1f MB final, %.1f MB peak",
            $totalCourses,
            $totalDuration,
            $avgTimePerCourse,
            $processedRecords,
            $assignmentCount,
            $finalMemory,
            $peakMemory
        ));
        return $processedRecords;
    }

    /**
     * Process sections for a specific course
     *
     * @param array $course Course data
     * @return int Number of processed records
     */
    private function processCourseSections($course)
    {
        $processedRecords = 0;
        $courseId = $course['id'];

        // Get sections for course
        $sections = $this->canvasApi->get("/api/v1/courses/{$courseId}/sections", [
            'per_page' => self::PER_PAGE_LIMIT
        ]);

        if ($sections !== false) {
            // Get grading periods for the course
            $gradingPeriods = $this->canvasApi->get("/api/v1/courses/{$courseId}/grading_periods");
            $currentGradingPeriod = null;
            if ($gradingPeriods && isset($gradingPeriods['grading_periods'])) {
                // Find current or most recent grading period
                foreach ($gradingPeriods['grading_periods'] as $gp) {
                    if (!$currentGradingPeriod ||
                        (isset($gp['end_date']) && strtotime($gp['end_date']) > strtotime($currentGradingPeriod['end_date']))) {
                        $currentGradingPeriod = $gp;
                    }
                }
            }

            // Process each section
            foreach ($sections as $section) {
                $sectionId = $section['id'];

                // Get enrollments with grades and user data
                $params = [
                    'type[]'           => 'StudentEnrollment',
                    'include[]'        => ['grades', 'user'],
                    'per_page'         => self::PER_PAGE_LIMIT,
                    'enrollment_state' => 'active'
                ];

                // Add grading period if available
                if ($currentGradingPeriod && isset($currentGradingPeriod['id'])) {
                    $params['grading_period_id'] = $currentGradingPeriod['id'];
                }

                $enrollments = $this->canvasApi->get("/api/v1/sections/{$sectionId}/enrollments", $params);

                if ($enrollments !== false && count($enrollments) > 0) {
                    $processedRecords += $this->processEnrollmentGrades($enrollments, $course, $section, $currentGradingPeriod);
                }
            }
        }

        return $processedRecords;
    }

    /**
     * Collect assignment data for a course
     *
     * @param array $course Course data
     */
    private function collectAssignmentData($course)
    {
        $courseId = $course['id'];

        // Get assignments
        $assignments = $this->canvasApi->get("/api/v1/courses/{$courseId}/assignments", [
            'per_page' => self::PER_PAGE_LIMIT
        ]);

        $assignmentLookup = [];
        if (is_array($assignments)) {
            foreach ($assignments as $a) {
                if (isset($a['id'])) {
                    $assignmentLookup[$a['id']] = $a;
                }
            }
        }

        // Get assignment groups for category mapping (with caching)
        if (!isset($this->assignmentGroupsCache[$courseId])) {
            $assignmentGroups = $this->canvasApi->get("/api/v1/courses/{$courseId}/assignment_groups", [
                'per_page' => self::PER_PAGE_LIMIT
            ]);
            $this->assignmentGroupsCache[$courseId] = $assignmentGroups;
        } else {
            $assignmentGroups = $this->assignmentGroupsCache[$courseId];
        }

        // Build assignment group lookup for categories
        $groupLookup = [];
        if (is_array($assignmentGroups)) {
            foreach ($assignmentGroups as $group) {
                if (isset($group['id'])) {
                    $groupLookup[$group['id']] = $group['name'] ?? '';
                }
            }
        }

        // Get course sections for section/teacher mapping (with caching)
        $sectionsCacheKey = "sections_{$courseId}";
        if (!isset($this->enrollmentCache[$sectionsCacheKey])) {
            $sections = $this->canvasApi->get("/api/v1/courses/{$courseId}/sections", [
                'per_page' => self::PER_PAGE_LIMIT
            ]);
            $this->enrollmentCache[$sectionsCacheKey] = $sections;
        } else {
            $sections = $this->enrollmentCache[$sectionsCacheKey];
        }

        // Get term code from course
        $termCode = $this->extractTermCode($course);
        $schoolCode = $this->extractSchoolCode($course, null);

        if (!empty($assignmentLookup)) {
            // Get bulk submissions WITHOUT comments for speed (2M assignments optimization)
            $submissions = $this->canvasApi->get("/api/v1/courses/{$courseId}/students/submissions", [
                'student_ids[]' => 'all',
                'include[]'     => ['assignment'],
                'per_page'      => self::PER_PAGE_LIMIT
            ]);

            // Get course enrollments to map Canvas user IDs to integration IDs and sections (with caching)
            $enrollmentsCacheKey = "enrollments_{$courseId}";
            if (!isset($this->enrollmentCache[$enrollmentsCacheKey])) {
                // Use maximum page size and only active enrollments for speed
                $courseEnrollments = $this->canvasApi->get("/api/v1/courses/{$courseId}/enrollments", [
                    'type[]'           => 'StudentEnrollment',
                    'include[]'        => 'user',
                    'per_page'         => self::PER_PAGE_LIMIT,
                    'enrollment_state' => 'active'
                ]);
                $this->enrollmentCache[$enrollmentsCacheKey] = $courseEnrollments;
            } else {
                $courseEnrollments = $this->enrollmentCache[$enrollmentsCacheKey];
            }

            // Build user mapping from cached enrollments
            $userMap = [];
            $userSectionMap = [];
            if (is_array($courseEnrollments)) {
                foreach ($courseEnrollments as $enrollment) {
                    if (isset($enrollment['user_id']) && isset($enrollment['user']['integration_id'])) {
                        $userMap[$enrollment['user_id']] = $enrollment['user']['integration_id'];
                        $userSectionMap[$enrollment['user_id']] = $enrollment['course_section_id'] ?? null;
                    }
                }
            }

            // Build section info lookup with bulk teacher loading
            $sectionInfo = [];
            if (is_array($sections)) {
                // Get ALL teachers for this course in ONE API call instead of per-section calls
                $teacherEnrollments = $this->canvasApi->get("/api/v1/courses/{$courseId}/enrollments", [
                    'type[]'    => 'TeacherEnrollment',
                    'include[]' => 'user',
                    'per_page'  => self::PER_PAGE_LIMIT
                ]);

                // Build teacher lookup by section ID and cache it
                $teachersBySection = [];
                if (is_array($teacherEnrollments)) {
                    foreach ($teacherEnrollments as $enrollment) {
                        $sectionId = $enrollment['course_section_id'] ?? null;
                        if ($sectionId && isset($enrollment['user'])) {
                            $user = $enrollment['user'];
                            $teachersBySection[$sectionId] = [
                                'name' => $user['name'] ?? '',
                                'id'   => $user['integration_id'] ?? $user['sis_user_id'] ?? ''
                            ];
                        }
                    }
                    // Cache teachers by course for getTeacherForSection optimization
                    $this->enrollmentCache["teachers_{$courseId}"] = $teachersBySection;
                }

                foreach ($sections as $section) {
                    $sectionId = $section['id'];
                    $teacherInfo = $teachersBySection[$sectionId] ?? ['name' => '', 'id' => ''];
                    $sectionCode = $this->extractSectionCode($section);

                    $sectionInfo[$sectionId] = [
                        'section_code' => $sectionCode,
                        'staff_id'     => $teacherInfo['id'],
                        'teacher_name' => $teacherInfo['name'],
                        'section_data' => $section
                    ];
                }
            }

            if (is_array($submissions)) {
                foreach ($submissions as $s) {
                    $aid = $s['assignment_id'] ?? null;
                    $userId = $s['user_id'] ?? null;

                    if ($aid && isset($assignmentLookup[$aid]) && $userId) {
                        $a = $assignmentLookup[$aid];

                        // Get integration ID from enrollment data
                        $integrationId = $userMap[$userId] ?? null;

                        // Skip if no integration ID found
                        if (!$integrationId) {
                            continue;
                        }

                        // Get user's section info
                        $userSectionId = $userSectionMap[$userId] ?? null;
                        $sectionData = $sectionInfo[$userSectionId] ?? [
                            'section_code' => (string)$courseId,
                            'staff_id'     => '',
                            'teacher_name' => '',
                            'section_data' => []
                        ];

                        // Get category from assignment group
                        $category = '';
                        if (isset($a['assignment_group_id']) && isset($groupLookup[$a['assignment_group_id']])) {
                            $category = $groupLookup[$a['assignment_group_id']];
                        }

                        // Skip comments for performance with 2M assignments
                        $commentText = '';

                        // Extract period from section name (e.g., "D1 B1/2" or "Block 1/2 Day 1")
                        $period = $this->extractPeriodFromSection($sectionData['section_data'] ?? $sectionData);

                        $assignmentData = [
                            'course_id'       => $courseId,
                            'course_name'     => $course['name'] ?? '',
                            'section_code'    => $sectionData['section_code'],
                            'school_code'     => $schoolCode,
                            'staff_id'        => $sectionData['staff_id'],
                            'term_code'       => $termCode,
                            'period'          => $period,
                            'assignment_id'   => $aid,
                            'assignment_name' => $a['name'] ?? '',
                            'description'     => $a['description'] ?? '',
                            'category'        => $category,
                            'student_id'      => $integrationId,
                            'canvas_user_id'  => $userId,
                            'score'           => $s['score'] ?? null,
                            'points_possible' => $a['points_possible'] ?? null,
                            'submitted_at'    => $s['submitted_at'] ?? null,
                            'graded_at'       => $s['graded_at'] ?? null,
                            'comment'         => $commentText,
                            'due_date'        => $a['due_at'] ?? null
                        ];

                        $this->assignmentsData[] = $assignmentData;
                        if (!$this->_assignmentsBatchingStarted) {
                            $this->assignmentsDataForGcs[] = $assignmentData;
                        }

                        // Debug log assignment collection (only first few)
                        if ($this->debugMode && count($this->assignmentsData) <= 3) {
                            $this->logInfo("Assignment collected: student={$integrationId}, assignment='{$a['name']}', score={$s['score']}, course={$courseId}, category={$category}");
                        }

                        // Process assignments in smaller batches for 2M assignment performance
                        if (count($this->assignmentsData) % 50000 === 0) {
                            $memoryUsage = memory_get_usage(true) / 1024 / 1024;
                            $this->logInfo("Assignment collection: " . count($this->assignmentsData) . " records, memory: " . round($memoryUsage, 2) . " MB");

                            // Process assignments more frequently with massive loads
                            if (count($this->assignmentsData) >= 100000) {
                                $this->logInfo("Processing assignment batch: " . count($this->assignmentsData) . " records");
                                $this->insertAssignmentsBatchFast($this->assignmentsData);
                                gc_collect_cycles();
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Process enrollment grades and collect for CSV export
     *
     * @param array $enrollments List of enrollments
     * @param array $course Course data
     * @param array $section Section data
     * @param array $currentGradingPeriod Current grading period data
     * @return int Number of processed records
     */
    private function processEnrollmentGrades($enrollments, $course, $section, $currentGradingPeriod = null)
    {
        $processedCount = 0;

        // Get teacher information for this section (with course-level caching)
        $teacherInfo = $this->getTeacherForSection($section['id'], $course['id']);

        // Extract section code from sis_section_id or use Canvas ID
        $sectionCode = $this->extractSectionCode($section);

        // Extract term code from course term
        $termCode = $this->extractTermCode($course);

        // Extract period from section name (e.g., "2,1" or "2,1 | 2,2")
        $period = $this->extractPeriodFromSection($section);

        // Extract school code early for debug logging
        $schoolCode = $this->extractSchoolCode($course, $section);

        // Build class name with grading period info
        $className = $this->buildClassName($course, $currentGradingPeriod, $section);

        foreach ($enrollments as $enrollment) {
            $studentCanvasId = $enrollment['user_id'];

            // Get integration_id from user data included in enrollment
            $studentId = null;
            if (isset($enrollment['user']['integration_id'])) {
                $studentId = $enrollment['user']['integration_id'];
            }

            // Skip if no integration ID (not a valid student)
            if (!$studentId) {
                continue;
            }

            // Extract grade data
            $currentGrade = $enrollment['grades']['current_grade'] ?? null;
            $currentScore = $enrollment['grades']['current_score'] ?? null;
            $finalGrade = $enrollment['grades']['final_grade'] ?? null;
            $finalScore = $enrollment['grades']['final_score'] ?? null;
            
            // Canvas returns F for grades with a 0
            //If current score is less than 0.001, set current grade to blank
            if ($currentScore !== null && floatval($currentScore) < 0.001) {
                $currentGrade = '';
            }

            // School code already extracted above for debug logging

            // School code extraction
            $gradeRecord = [
                'student_id'         => $studentId,
                'canvas_student_id'  => $studentCanvasId,
                'course_id'          => $course['id'],
                'course_name'        => $className,  // Use formatted class name
                'section_id'         => $section['id'],
                'section_code'       => $sectionCode,
                'section_name'       => $section['name'],
                'current_grade'      => $currentGrade,
                'current_score'      => $currentScore,
                'final_grade'        => $finalGrade,
                'final_score'        => $finalScore,
                'enrollment_state'   => $enrollment['enrollment_state'] ?? 'active',
                'last_activity_at'   => $enrollment['last_activity_at'] ?? null,
                'teacher_name'       => $teacherInfo['name'] ?? '',
                'staff_id'           => $teacherInfo['id'] ?? '',
                'term_code'          => $termCode,
                'school_code'        => $schoolCode,
                'period'             => $period
            ];

            // Collect grade data for CSV export
            $this->gradesData[] = $gradeRecord;
            $processedCount++;
        }

        return $processedCount;
    }

    /**
     * Get collected grades data
     *
     * @return array Grades data
     */
    public function getGradesData()
    {
        return $this->gradesData;
    }


    /**
     * Clear grades data after all batches have been processed
     */
    public function clearGradesDataAfterBatching()
    {
        $this->gradesData = [];
        gc_collect_cycles();
    }

    /**
     * Get collected assignments data
     *
     * @return array Assignments data
     */
    public function getAssignmentsData()
    {
        return $this->assignmentsData;
    }

    /**
     * Get collected assignments data for GCS upload
     *
     * @return array Assignments data for GCS
     */
    public function getAssignmentsDataForGcs()
    {
        return $this->assignmentsDataForGcs;
    }

    /**
     * Get assignments data for GCS in batches for memory-efficient processing
     * This method chunks the data but preserves it until all batches are consumed
     *
     * @param int $batchSize Batch size for processing
     * @return array Batched assignments data for GCS
     */
    public function getAssignmentsDataForGcsInBatches($batchSize = null)
    {
        if ($batchSize === null) {
            $batchSize = self::GCS_UPLOAD_BATCH_SIZE;
        }

        if (empty($this->assignmentsDataForGcs)) {
            return [];
        }

        // Create batches from current data without clearing yet
        $batches = array_chunk($this->assignmentsDataForGcs, $batchSize);

        // Mark that we've started batching to prevent further data addition
        $this->_assignmentsBatchingStarted = true;

        return $batches;
    }

    /**
     * Clear assignments data after all batches have been processed
     */
    public function clearAssignmentsDataAfterBatching()
    {
        $this->assignmentsDataForGcs = [];
        $this->_assignmentsBatchingStarted = false;
        gc_collect_cycles();
    }

    /**
     * Execute a database query with lightweight logic for connection issues
     *
     * @param string $sql The SQL query to execute
     * @param int $maxRetries Maximum number of retry attempts
     * @return bool True on success, false on failure
     */
    private function executeQueryWithRetry($sql, $maxRetries = 2)
    {
        $attempt = 1;

        while ($attempt <= $maxRetries) {
            $result = $this->db->query($sql);

            if ($result) {
                return true;
            }

            $error = $this->db->error;

            // Check if it's a connection-related error
            if (stripos($error, 'server has gone away') !== false ||
                stripos($error, 'lost connection') !== false) {

                $this->logInfo("Database connection issue detected (attempt $attempt/$maxRetries): $error");

                if ($attempt < $maxRetries) {
                    // Brief pause before retry
                    sleep(1);

                    // Simple ping test - let MySQL handle reconnection automatically
                    if (!$this->db->ping()) {
                        $this->logInfo("Connection lost, MySQL will auto-reconnect on next query");
                    }
                }
            } else {
                // Non-connection error, don't retry
                break;
            }

            $attempt++;
        }

        return false;
    }



    /**
     * Clear existing assignments for this site/year
     *
     * @throws Exception When database operation fails
     */
    private function clearExistingAssignments()
    {
        $sql = "DELETE FROM abre_assignments WHERE site_id = ? AND school_year_id = ?";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            // If abre_assignments doesn't exist, skip clearing
            $this->logInfo("No assignments table found");
            return;
        }
        $stmt->bind_param("ii", $this->siteId, $this->schoolYearId);

        if (!$stmt->execute()) {
            throw new Exception("Failed to clear existing assignments: " . $stmt->error);
        }

        $stmt->close();
        $this->logInfo("Cleared existing assignments for site {$this->siteId}");
    }

    /**
     * Clear existing class posts and related data for this site/year - Canvas assignments only
     *
     * @throws Exception When database operation fails
     */
    private function clearExistingClassPosts()
    {
        try {
            // Clear class_scores first (foreign key constraint) - only for Canvas activities
            $sql = "DELETE cs FROM class_scores cs
                    JOIN class_posts cp ON cs.post_id = cp.id
                    WHERE cp.site_id = ? AND cp.school_year_id = ? AND cp.type = 'Activity'
                    AND (cp.instructions LIKE '%Canvas%' OR cp.instructions LIKE '%Imported from Canvas%')";
            $stmt = $this->db->prepare($sql);
            if ($stmt) {
                $stmt->bind_param("ii", $this->siteId, $this->schoolYearId);
                $stmt->execute();
                $affected = $stmt->affected_rows;
                $stmt->close();
                $this->logInfo("Cleared {$affected} existing class_scores for Canvas activities");
            }

            // Clear class_activity_students (foreign key constraint) - only for Canvas activities
            $sql = "DELETE cas FROM class_activity_students cas
                    JOIN class_posts cp ON cas.activity_id = cp.id
                    WHERE cp.site_id = ? AND cp.school_year_id = ? AND cp.type = 'Activity'
                    AND (cp.instructions LIKE '%Canvas%' OR cp.instructions LIKE '%Imported from Canvas%')";
            $stmt = $this->db->prepare($sql);
            if ($stmt) {
                $stmt->bind_param("ii", $this->siteId, $this->schoolYearId);
                $stmt->execute();
                $affected = $stmt->affected_rows;
                $stmt->close();
                $this->logInfo("Cleared {$affected} existing class_activity_students for Canvas activities");
            }

            // Clear class_posts for Canvas activities only
            $sql = "DELETE FROM class_posts
                    WHERE site_id = ? AND school_year_id = ? AND type = 'Activity'
                    AND (instructions LIKE '%Canvas%' OR instructions LIKE '%Imported from Canvas%')";
            $stmt = $this->db->prepare($sql);
            if ($stmt) {
                $stmt->bind_param("ii", $this->siteId, $this->schoolYearId);
                $stmt->execute();
                $affected = $stmt->affected_rows;
                $stmt->close();
                $this->logInfo("Cleared {$affected} existing class_posts for Canvas activities");
            }

        } catch (Exception $e) {
            // Log but don't fail - tables might not exist
            $this->logInfo("Could not clear class_posts tables (may not exist): " . $e->getMessage());
        }
    }

    /**
     * Check if course is a template/sample course
     *
     * @param string $courseName Course name to check
     * @return bool True if template course, false otherwise
     */
    private function isTemplateCourse($courseName)
    {
        $templateKeywords = ['template', 'sample', 'example', 'test'];
        $lowerName = strtolower($courseName);

        foreach ($templateKeywords as $keyword) {
            if (strpos($lowerName, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Fast assignment batch insert - also populates class_posts for main app compatibility
     *
     * @param array $assignments Assignment data to insert
     * @return int Number of assignments inserted
     */
    private function insertAssignmentsBatchFast($assignments)
    {
        if (empty($assignments)) {
            return 0;
        }

        try {
            // Start transaction for data integrity
            $this->db->begin_transaction();

            // Fast bulk insert to abre_assignments
            $values = [];
            $classPostsData = [];
            $insertedCount = 0;

            foreach ($assignments as $assignment) {
                $studentId = $this->db->real_escape_string((string) ($assignment['student_id'] ?? ''));
                $courseCode = $this->db->real_escape_string((string) ($assignment['course_id'] ?? ''));
                $sectionCode = $this->db->real_escape_string((string) ($assignment['section_code'] ?? ''));
                $schoolCode = $this->db->real_escape_string((string) ($assignment['school_code'] ?? ''));
                $staffId = $this->db->real_escape_string((string) ($assignment['staff_id'] ?? ''));
                $termCode = $this->db->real_escape_string((string) ($assignment['term_code'] ?? ''));
                $title = $this->db->real_escape_string((string) ($assignment['assignment_name'] ?? ''));
                $description = $this->db->real_escape_string((string) ($assignment['description'] ?? ''));
                $dueDate = $assignment['due_date'] ? "'" . $this->db->real_escape_string((string) $assignment['due_date']) . "'" : 'NULL';
                $category = $this->db->real_escape_string((string) ($assignment['category'] ?? ''));
                $comment = $this->db->real_escape_string((string) ($assignment['comment'] ?? ''));
                $earnedPoints = isset($assignment['score']) && $assignment['score'] !== null ? (float) $assignment['score'] : 'NULL';
                $possiblePoints = isset($assignment['points_possible']) && $assignment['points_possible'] !== null ? (float) $assignment['points_possible'] : 'NULL';

                // Skip if no student ID
                if (empty($studentId)) {
                    continue;
                }

                // Get period from assignment data
                $period = isset($assignment['period']) ? $this->db->real_escape_string((string) $assignment['period']) : '1';

                $values[] = "('{$studentId}', '{$courseCode}', '{$sectionCode}', '{$schoolCode}', '{$staffId}', '{$termCode}', '{$period}', '{$title}', '{$description}', {$dueDate}, '{$category}', {$earnedPoints}, {$possiblePoints}, NULL, '{$comment}', 'Y', {$this->siteId}, {$this->schoolYearId})";

                // Prepare data for class_posts insertion
                $assignmentId = $assignment['assignment_id'] ?? '';
                $classPostsData[] = [
                    'assignment_id'   => $assignmentId,
                    'student_id'      => $studentId,
                    'course_code'     => $courseCode,
                    'section_code'    => $sectionCode,
                    'school_code'     => $schoolCode,
                    'staff_id'        => $staffId,
                    'term_code'       => $termCode,
                    'title'           => $title,
                    'description'     => $description,
                    'due_date'        => $assignment['due_date'] ?? null,
                    'category'        => $category,
                    'earned_points'   => $earnedPoints,
                    'possible_points' => $possiblePoints,
                    'period'          => $period
                ];

                $insertedCount++;
            }

            if (!empty($values)) {
                // Insert into abre_assignments first
                $chunks = array_chunk($values, self::ASSIGNMENT_BATCH_SIZE);

                foreach ($chunks as $chunkIndex => $chunk) {
                    $sql = "INSERT INTO abre_assignments (
                        student_id, course_code, section_code, school_code, staff_id, term_code, period,
                        title, description, due_date, category, earned_points, possible_points,
                        weight_percentage, comment, published, site_id, school_year_id
                    ) VALUES " . implode(',', $chunk);

                    if (!$this->db->query($sql)) {
                        $this->logError("Failed to insert assignment chunk: " . $this->db->error);
                        return 0;
                    }

                    if ($this->debugMode && ($chunkIndex + 1) % 10 === 0) {
                        $this->logInfo("Inserted assignment chunk " . ($chunkIndex + 1) . " with " . count($chunk) . " assignments");
                    }

                    // Force garbage collection less frequently for speed
                    if (($chunkIndex + 1) % 20 === 0) {
                        gc_collect_cycles();
                    }
                }

                // Now insert into class_posts and related tables for main app compatibility
                $this->insertClassPostsFromAssignments($classPostsData);

            } else {
                if ($this->debugMode) {
                    $this->logInfo("No assignment values to insert");
                }
            }

            // Commit transaction if everything succeeded (includes all S360 tables)
            $this->db->commit();
            return $insertedCount;

        } catch (Exception $e) {
            // Rollback transaction on any error
            $this->db->rollback();
            $this->logError("Fast assignment insert failed: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Insert assignment data into class_posts and related tables for main app compatibility
     *
     * @param array $classPostsData Array of assignment data to convert to class posts
     */
    private function insertClassPostsFromAssignments($classPostsData)
    {
        if (empty($classPostsData)) {
            return;
        }

        try {
            // Group assignments by unique assignment (assignment_id + course + section)
            $uniqueAssignments = [];
            $studentScores = [];

            foreach ($classPostsData as $assignment) {
                $assignmentKey = $assignment['assignment_id'] . '_' . $assignment['course_code'] . '_' . $assignment['section_code'];

                // Store unique assignment data
                if (!isset($uniqueAssignments[$assignmentKey])) {
                    $uniqueAssignments[$assignmentKey] = [
                        'assignment_id'   => $assignment['assignment_id'],
                        'course_code'     => $assignment['course_code'],
                        'section_code'    => $assignment['section_code'],
                        'school_code'     => $assignment['school_code'],
                        'staff_id'        => $assignment['staff_id'],
                        'term_code'       => $assignment['term_code'],
                        'title'           => $assignment['title'],
                        'description'     => $assignment['description'],
                        'due_date'        => $assignment['due_date'],
                        'category'        => $assignment['category'],
                        'possible_points' => $assignment['possible_points'],
                        'period'          => $assignment['period']
                    ];
                }

                // Store student score data
                if (!empty($assignment['student_id'])) {
                    $studentScores[] = [
                        'assignment_key' => $assignmentKey,
                        'student_id'     => $assignment['student_id'],
                        'earned_points'  => $assignment['earned_points']
                    ];
                }
            }

            // Insert unique assignments into class_posts
            $classPostValues = [];
            foreach ($uniqueAssignments as $assignment) {
                $title = $this->db->real_escape_string($this->stripUnsupportedChars($assignment['title']));
                // SAFETY: Mark Canvas assignments clearly in instructions
                $originalInstructions = $this->stripUnsupportedChars($assignment['description'] ?? '');
                $canvasMarker = 'Imported from Canvas';
                $description = $this->db->real_escape_string($originalInstructions . ($originalInstructions ? "\n\n" : '') . $canvasMarker);

                $dueDate = $assignment['due_date'] ? "'" . $this->db->real_escape_string($assignment['due_date']) . "'" : 'NULL';
                $points = isset($assignment['possible_points']) && $assignment['possible_points'] !== 'NULL' ? (float)$assignment['possible_points'] : 0;
                $categoryId = !empty($assignment['category']) ? $this->getCategoryId($assignment['category'], $assignment['course_code'], $assignment['section_code'], $assignment['school_code'], $assignment['term_code']) : 'NULL';

                $classPostValues[] = "(
                    '{$assignment['school_code']}',
                    '{$assignment['course_code']}',
                    '{$assignment['section_code']}',
                    '{$assignment['term_code']}',
                    'Activity',
                    '{$title}',
                    '{$description}',
                    {$dueDate},
                    {$points},
                    {$this->siteId},
                    {$categoryId},
                    {$this->schoolYearId},
                    NULL
                )";
            }

            if (!empty($classPostValues)) {
                $sql = "INSERT INTO class_posts (
                    school_code, course_code, section_code, term_code, type, title, instructions,
                    due_date, points, site_id, category_id, school_year_id, topic_id
                ) VALUES " . implode(',', $classPostValues) . "
                ON DUPLICATE KEY UPDATE
                    title = VALUES(title),
                    instructions = VALUES(instructions),
                    points = VALUES(points),
                    due_date = VALUES(due_date)";

                if (!$this->db->query($sql)) {
                    $this->logError("Failed to insert class_posts: " . $this->db->error);
                    throw new Exception("Critical database error: Failed to insert class_posts");
                }

                if ($this->debugMode) {
                    $this->logInfo("Inserted " . count($classPostValues) . " class_posts records");
                }
            }

            // Insert student scores into class_scores if we have scores
            $this->insertClassScores($studentScores, $uniqueAssignments);

            // Insert student-assignment links into class_activity_students for S360 display
            $this->insertClassActivityStudents($studentScores, $uniqueAssignments);

        } catch (Exception $e) {
            $this->logError("Failed to insert class_posts data: " . $e->getMessage());
            // Re-throw to ensure transaction rollback
            throw $e;
        }
    }

    /**
     * Insert student scores into class_scores table
     *
     * @param array $studentScores Array of student score data
     * @param array $uniqueAssignments Array of unique assignment data
     */
    private function insertClassScores($studentScores, $uniqueAssignments)
    {
        if (empty($studentScores)) {
            return;
        }

        try {
            // First, get the post_ids that were just inserted
            $assignmentKeys = array_keys($uniqueAssignments);
            $postIdMapping = [];

            foreach ($assignmentKeys as $assignmentKey) {
                $assignment = $uniqueAssignments[$assignmentKey];

                // Query to get the post_id for this assignment
                $sql = "SELECT id FROM class_posts
                        WHERE site_id = ? AND course_code = ? AND section_code = ? AND term_code = ?
                        AND school_year_id = ? AND title = ? AND type = 'Activity'
                        ORDER BY id DESC LIMIT 1";

                $stmt = $this->db->prepare($sql);
                if ($stmt) {
                    if (!$stmt->bind_param('isssis',
                        $this->siteId,
                        $assignment['course_code'],
                        $assignment['section_code'],
                        $assignment['term_code'],
                        $this->schoolYearId,
                        $assignment['title']
                    )) {
                        $this->logError("Failed to bind parameters in insertClassScores: " . $stmt->error);
                        $stmt->close();
                        continue;
                    }

                    if (!$stmt->execute()) {
                        $this->logError("Failed to execute statement in insertClassScores: " . $stmt->error);
                        $stmt->close();
                        continue;
                    }

                    $result = $stmt->get_result();

                    if ($row = $result->fetch_assoc()) {
                        $postIdMapping[$assignmentKey] = $row['id'];
                    }
                    $stmt->close();
                } else {
                    $this->logError("Failed to prepare statement in insertClassScores: " . $this->db->error);
                }
            }

            // Now insert class_scores
            $scoreValues = [];
            foreach ($studentScores as $score) {
                if (isset($postIdMapping[$score['assignment_key']]) &&
                    !empty($score['student_id']) &&
                    $score['earned_points'] !== 'NULL') {

                    $postId = $postIdMapping[$score['assignment_key']];
                    $studentId = $this->db->real_escape_string($score['student_id']);
                    $earnedPoints = (float)$score['earned_points'];

                    $scoreValues[] = "(
                        {$postId},
                        '{$studentId}',
                        {$earnedPoints},
                        {$this->siteId},
                        '',
                        0,
                        0,
                        ''
                    )";
                }
            }

            if (!empty($scoreValues)) {
                $sql = "INSERT INTO class_scores (
                    post_id, student_id, score, site_id, comment, is_missing, is_excused, notification_status
                ) VALUES " . implode(',', $scoreValues) . "
                ON DUPLICATE KEY UPDATE
                    score = VALUES(score)";

                if (!$this->db->query($sql)) {
                    $this->logError("Failed to insert class_scores: " . $this->db->error);
                    throw new Exception("Critical database error: Failed to insert class_scores");
                }

                if ($this->debugMode) {
                    $this->logInfo("Inserted " . count($scoreValues) . " class_scores records");
                }
            }

        } catch (Exception $e) {
            $this->logError("Failed to insert class_scores: " . $e->getMessage());
        }
    }

    /**
     * Insert student-assignment links into class_activity_students table for S360 display
     *
     * @param array $studentScores Array of student score data
     * @param array $uniqueAssignments Array of unique assignment data
     */
    private function insertClassActivityStudents($studentScores, $uniqueAssignments)
    {
        if (empty($studentScores)) {
            return;
        }

        try {
            // Get the post_ids that were just inserted (reuse mapping logic from class_scores)
            $assignmentKeys = array_keys($uniqueAssignments);
            $postIdMapping = [];

            foreach ($assignmentKeys as $assignmentKey) {
                $assignment = $uniqueAssignments[$assignmentKey];

                // Query to get the post_id for this assignment
                $sql = "SELECT id FROM class_posts
                        WHERE site_id = ? AND course_code = ? AND section_code = ? AND term_code = ?
                        AND school_year_id = ? AND title = ? AND type = 'Activity'
                        ORDER BY id DESC LIMIT 1";

                $stmt = $this->db->prepare($sql);
                if ($stmt) {
                    if (!$stmt->bind_param('isssis',
                        $this->siteId,
                        $assignment['course_code'],
                        $assignment['section_code'],
                        $assignment['term_code'],
                        $this->schoolYearId,
                        $assignment['title']
                    )) {
                        $this->logError("Failed to bind parameters in insertClassActivityStudents: " . $stmt->error);
                        $stmt->close();
                        continue;
                    }

                    if (!$stmt->execute()) {
                        $this->logError("Failed to execute statement in insertClassActivityStudents: " . $stmt->error);
                        $stmt->close();
                        continue;
                    }

                    $result = $stmt->get_result();

                    if ($row = $result->fetch_assoc()) {
                        $postIdMapping[$assignmentKey] = $row['id'];
                    }
                    $stmt->close();
                } else {
                    $this->logError("Failed to prepare statement in insertClassActivityStudents: " . $this->db->error);
                }
            }

            // Insert student-assignment links
            $activityStudentValues = [];
            $seenStudentPostPairs = [];

            foreach ($studentScores as $score) {
                if (isset($postIdMapping[$score['assignment_key']]) && !empty($score['student_id'])) {
                    $postId = $postIdMapping[$score['assignment_key']];
                    $studentId = $this->db->real_escape_string($score['student_id']);

                    // Ensure unique student-post combinations
                    $pairKey = $postId . '_' . $studentId;
                    if (!isset($seenStudentPostPairs[$pairKey])) {
                        $seenStudentPostPairs[$pairKey] = true;

                        $activityStudentValues[] = "(
                            {$postId},
                            '{$studentId}',
                            {$this->siteId},
                            CURRENT_TIMESTAMP
                        )";
                    }
                }
            }

            if (!empty($activityStudentValues)) {
                $sql = "INSERT INTO class_activity_students (
                    activity_id, student_id, site_id, last_modified_cdc
                ) VALUES " . implode(',', $activityStudentValues) . "
                ON DUPLICATE KEY UPDATE
                    last_modified_cdc = VALUES(last_modified_cdc)";

                if (!$this->db->query($sql)) {
                    $this->logError("Failed to insert class_activity_students: " . $this->db->error);
                    throw new Exception("Critical database error: Failed to insert class_activity_students");
                }

                if ($this->debugMode) {
                    $this->logInfo("Inserted " . count($activityStudentValues) . " class_activity_students records");
                }
            }

        } catch (Exception $e) {
            $this->logError("Failed to insert class_activity_students: " . $e->getMessage());
        }
    }

    /**
     * Get or create category ID for assignment category
     *
     * @param string $categoryName Category name
     * @param string $courseCode Course code
     * @param string $sectionCode Section code
     * @param string $schoolCode School code
     * @param string $termCode Term code
     * @return string Category ID or 'NULL'
     */
    private function getCategoryId($categoryName, $courseCode, $sectionCode, $schoolCode, $termCode)
    {
        if (empty($categoryName)) {
            return 'NULL';
        }

        // Remove emoji and other unsupported characters
        $categoryName = trim($this->stripUnsupportedChars((string) $categoryName));
        if ($categoryName === '') {
            return 'NULL';
        }

        try {
            // Check if category exists
            $sql = 'SELECT id FROM class_categories
                    WHERE site_id = ? AND school_code = ? AND course_code = ? AND section_code = ?
                    AND term_code = ? AND school_year_id = ? AND name = ?';

            $stmt = $this->db->prepare($sql);
            if ($stmt) {
                $stmt->bind_param(
                    'issssis',
                    $this->siteId,
                    $schoolCode,
                    $courseCode,
                    $sectionCode,
                    $termCode,
                    $this->schoolYearId,
                    $categoryName
                );
                if (!$stmt->execute()) {
                    $this->logError('Failed to execute category lookup: ' . $stmt->error);
                } else {
                    $result = $stmt->get_result();
                    if ($result && ($row = $result->fetch_assoc())) {
                        $stmt->close();
                        return $row['id'];
                    }
                }
                $stmt->close();
            }

            // Category doesn't exist, create it
            $sql = 'INSERT INTO class_categories (
                site_id, school_code, course_code, section_code, term_code, school_year_id,
                name, weight, created_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 100, NOW())';

            $stmt = $this->db->prepare($sql);
            if ($stmt) {
                $stmt->bind_param(
                    'issssis',
                    $this->siteId,
                    $schoolCode,
                    $courseCode,
                    $sectionCode,
                    $termCode,
                    $this->schoolYearId,
                    $categoryName
                );
                if (!$stmt->execute()) {
                    $this->logError('Failed to insert category: ' . $stmt->error);
                    $stmt->close();
                    return 'NULL';
                }
                $categoryId = $this->db->insert_id;
                $stmt->close();

                return $categoryId;
            }

        } catch (Exception $e) {
            $this->logError("Failed to get/create category: " . $e->getMessage());
        }

        return 'NULL';
    }

    /**
     * Log info message with context
     *
     * @param string $message Message to log
     */
    private function logInfo($message)
    {
        $context = "Site: {$this->siteId}, Year: {$this->schoolYearId}";
        if ($this->jobUuid) {
            $context .= ", Job: {$this->jobUuid}";
        }
        error_log("Canvas: {$message} [{$context}]");
    }
    /**
     * Strip characters not supported by database encoding (e.g., emoji)
     *
     * @param string|null $value
     * @return string
     */
    private function stripUnsupportedChars($value)
    {
        if ($value === null) {
            return '';
        }

        $value = (string) $value;

        // Remove any 4-byte UTF-8 characters (non-BMP), such as emojis
        $sanitized = preg_replace('/[\x{10000}-\x{10FFFF}]/u', '', $value);

        // Fallback: remove raw 4-byte sequences if Unicode regex fails
        if ($sanitized === null) {
            $sanitized = preg_replace('/[\xF0-\xF7][\x80-\xBF]{3}/', '', $value);
        }

        return $sanitized ?? '';
    }


    /**
     * Log error message with context
     *
     * @param string $message Error message to log
     */
    private function logError($message)
    {
        $context = "Site: {$this->siteId}, Year: {$this->schoolYearId}";
        if ($this->jobUuid) {
            $context .= ", Job: {$this->jobUuid}";
        }
        error_log("Canvas: {$message} [{$context}]");
    }

    /**
     * Get teacher information for a section - OPTIMIZED to use course-level cache
     * This avoids individual API calls per section
     *
     * @param int $sectionId Section ID
     * @param int $courseId Course ID for bulk teacher lookup
     * @return array Teacher info with 'name' and 'id' keys
     */
    private function getTeacherForSection($sectionId, $courseId = null)
    {
        // Check cache first
        if (isset($this->sectionTeacherCache[$sectionId])) {
            return $this->sectionTeacherCache[$sectionId];
        }

        // If we have a course ID, try to get teacher from course-level cache
        if ($courseId) {
            $courseCacheKey = "teachers_{$courseId}";
            if (isset($this->enrollmentCache[$courseCacheKey])) {
                $teachersBySection = $this->enrollmentCache[$courseCacheKey];
                if (isset($teachersBySection[$sectionId])) {
                    $this->sectionTeacherCache[$sectionId] = $teachersBySection[$sectionId];
                    return $teachersBySection[$sectionId];
                }
            }
        }

        // Fallback to individual API call
        $teacherEnrollments = $this->canvasApi->get("/api/v1/sections/{$sectionId}/enrollments", [
            'type[]'    => 'TeacherEnrollment',
            'include[]' => 'user',
            'per_page'  => self::PER_PAGE_LIMIT
        ]);

        $result = ['name' => '', 'id' => ''];

        if (!empty($teacherEnrollments) && isset($teacherEnrollments[0]['user'])) {
            $teacher = $teacherEnrollments[0]['user'];
            $result = [
                'name' => $teacher['name'] ?? '',
                'id'   => $teacher['integration_id'] ?? $teacher['sis_user_id'] ?? ''
            ];
        }

        // Cache the result
        $this->sectionTeacherCache[$sectionId] = $result;
        return $result;
    }

    /**
     * Extract section code from section data
     *
     * @param array $section Section data
     * @return string Section code
     */
    private function extractSectionCode($section)
    {
        // Try to extract from sis_section_id if it exists
        if (!empty($section['sis_section_id'])) {
            // Try the pattern extraction first
            if (preg_match('/Section_(\d+)$/', $section['sis_section_id'], $matches)) {
                return $matches[1];
            }
            // Otherwise use the full sis_section_id
            return (string) $section['sis_section_id'];
        }

        // Fallback to Canvas section ID
        return (string) $section['id'];
    }

    /**
     * Extract term code from course data
     *
     * @param array $course Course data
     * @return string Term code
     */
    private function extractTermCode($course)
    {
        // Look up the term if we have enrollment_term_id
        if (!empty($course['enrollment_term_id'])) {
            $termId = $course['enrollment_term_id'];

            // Check cache first
            if (!isset($this->termCache[$termId])) {
                // Get term details from Canvas using stored account ID
                $termData = $this->canvasApi->get("/api/v1/accounts/1/terms/{$termId}");
                $this->termCache[$termId] = $termData;
            } else {
                $termData = $this->termCache[$termId];
            }

            if ($termData && isset($termData['name'])) {
                // Extract term code from name (e.g., "2024/2025 - High School - T1" -> "T1")
                if (preg_match('/-\s*([A-Z0-9]+)$/', $termData['name'], $matches)) {
                    $extracted = $matches[1];
                    // Convert SM1/SM2/SO1/SO2 to T codes if needed
                    if ($extracted === 'SM1' || $extracted === 'SO1') return 'T1';
                    if ($extracted === 'SM2' || $extracted === 'SO2') return 'T3';
                    if (in_array($extracted, ['T1', 'T2', 'T3', 'T4'])) return $extracted;
                }
            }
        }

        return '';
    }

    /**
     * Extract period information from section name
     *
     * @param array $section Section data
     * @return string Period info (e.g., "2,1" or "2,1 | 2,2")
     */
    private function extractPeriodFromSection($section)
    {
        $sectionName = $section['name'] ?? '';

        // Extract period info from various section name formats:
        // "D1 B1/2", "B 1/2 D2", "B1/2 D1 SM1", "B5/6 D1 SM1", "B3/4 D1 SM1"

        // Pattern 1: "D1 B1/2" or "D2 B3/4"
        if (preg_match('/\b(D\d+\s+B\d+\/\d+)\b/i', $sectionName, $matches)) {
            return $matches[1];
        }

        // Pattern 2: "B 1/2 D2" or "B1/2 D1"
        if (preg_match('/\b(B\s*\d+\/\d+\s+D\d+)\b/i', $sectionName, $matches)) {
            return $matches[1];
        }

        // Pattern 3: Just "B1/2", "B3/4", "B5/6" etc.
        if (preg_match('/\b(B\d+\/\d+)\b/i', $sectionName, $matches)) {
            return $matches[1];
        }

        // Pattern 4: "Block X/Y Day Z" (original pattern)
        if (preg_match('/Block\s+(\d+\/\d+)\s+Day\s+(\d+)/i', $sectionName, $matches)) {
            return "Block {$matches[1]} Day {$matches[2]}";
        }

        // Pattern 5: "SM1-5", "T1-3", etc. (like "2026-VISUAL ARTS 6-MOUA-SM1-5")
        if (preg_match('/(SM\d+-\d+)$/i', $sectionName, $matches)) {
            return $matches[1];
        }

        // Fallback: try the original pattern (e.g., "2,1 | 2,2")
        if (preg_match('/(\d+,\d+(?:\s*\|\s*\d+,\d+)*)/', $sectionName, $matches)) {
            return $matches[1];
        }

        // If no pattern found, just return the section name or a default
        return $sectionName ?: 'Unknown';
    }

    /**
     * Build formatted class name with grading period information
     *
     * @param array $course Course data
     * @param array $currentGradingPeriod Current grading period data
     * @param array $section Section data
     * @return string Formatted class name
     */
    private function buildClassName($course, $currentGradingPeriod, $section)
    {
        $courseName = $course['name'] ?? '';

        // Remove year from course name if present (e.g., "2024-2025 ALGEBRA 2" or "2026-VISUAL ARTS" -> "ALGEBRA 2" or "VISUAL ARTS")
        $courseName = preg_replace('/^\d{4}-\d{4}\s*/', '', $courseName);
        $courseName = preg_replace('/^\d{4}\/\d{4}\s*/', '', $courseName);
        $courseName = preg_replace('/^\d{4}-/', '', $courseName);  // Remove year prefix like "2026-"

        // Remove teacher name from course name (everything after last dash)
        $courseName = preg_replace('/-[A-Z]+$/', '', $courseName);

        // Clean up extra spaces and dashes
        $courseName = trim($courseName, ' -');

        // Get section name info for period details
        $sectionName = $section['name'] ?? '';

        // Extract period info from section name (e.g., "2,1 | 2,2")
        $periodInfo = '';
        if (preg_match('/(\d+,\d+(?:\s*\|\s*\d+,\d+)*)/', $sectionName, $matches)) {
            $periodInfo = $matches[1];
        }

        // Get grading period ID and course enrollment term ID
        $gradingPeriodId = $currentGradingPeriod['id'] ?? '';
        $termId = $course['enrollment_term_id'] ?? '';

        // Format: "[period_info] COURSE NAME (grading_period_id - term_id)"
        $classNameFormatted = $courseName;

        // Add grading period info if available
        if ($gradingPeriodId && $termId) {
            $classNameFormatted .= " ({$gradingPeriodId} - {$termId})";
        }

        // Add period info at the beginning if available
        if ($periodInfo) {
            $classNameFormatted = $periodInfo . ' ' . $classNameFormatted;
        }

        return $classNameFormatted;
    }

    /**
     * Force aggressive memory cleanup
     */
    private function forceMemoryCleanup()
    {
        // Flush any buffered assignments to database first
        if (!empty($this->assignmentsData)) {
            $this->insertAssignmentsBatchFast($this->assignmentsData);
            $this->assignmentsData = [];
        }

        // Note: Keep assignmentsDataForGcs for potential GCS export

        // Force garbage collection multiple times
        for ($i = 0; $i < 5; $i++) {
            gc_collect_cycles();
        }

        // Log memory usage after cleanup
        $memoryUsage = memory_get_usage(true) / 1024 / 1024;
        $peakMemory = memory_get_peak_usage(true) / 1024 / 1024;
        $this->logInfo("Memory cleanup completed. Current: " . round($memoryUsage, 2) . " MB, Peak: " . round($peakMemory, 2) . " MB");
    }

    /**
     * Extract school code from course/section data
     *
     * @param array $course Course data
     * @param array $section Section data
     * @return string School code
     */
    private function extractSchoolCode($course, $section)
    {
        // Define school name to code mapping for Wayzata (3-digit numeric codes)
        $schoolMapping = [
            'Wayzata High School'           => '049',
            'High School'                   => '049',
            'Wayzata Central Middle School' => '041',
            'Central Middle School'         => '041',
            'Wayzata East Middle School'    => '042',
            'East Middle School'            => '042',
            'Wayzata West Middle School'    => '043',
            'West Middle School'            => '043',
            'Meadow Ridge Elementary'       => '011',
            'North Woods Elementary'        => '012',
            'Gleason Lake Elementary'       => '013',
            'Kimberly Lane Elementary'      => '014',
            'Plymouth Creek Elementary'     => '015',
            'Sunset Hill Elementary'        => '016',
            'Oakwood Elementary'            => '017',
            'Birchview Elementary'          => '018',
            'Greenwood Elementary'          => '019',
            'Wayzata Transition School'     => '020',
            'School 050'                    => '050',
            'School 051'                    => '051',
            'School 052'                    => '052',
            'School 053'                    => '053'
        ];

        // First try to extract from course enrollment term name if available
        if (!empty($course['enrollment_term_id'])) {
            $termId = $course['enrollment_term_id'];

            // Use cached term data if available
            if (!isset($this->termCache[$termId])) {
                $termData = $this->canvasApi->get("/api/v1/accounts/1/terms/{$termId}");
                $this->termCache[$termId] = $termData;
            } else {
                $termData = $this->termCache[$termId];
            }

            if ($termData && isset($termData['name'])) {
                // Check if any school name is in the term name
                foreach ($schoolMapping as $schoolName => $schoolCode) {
                    if (strpos($termData['name'], $schoolName) !== false) {
                        return $schoolCode;
                    }
                }
            }
        }

        // Try to extract from course name
        $courseName = $course['name'] ?? '';
        foreach ($schoolMapping as $schoolName => $schoolCode) {
            if (strpos($courseName, $schoolName) !== false) {
                return $schoolCode;
            }
        }

        // Try to extract from section name
        $sectionName = $section['name'] ?? '';
        foreach ($schoolMapping as $schoolName => $schoolCode) {
            if (strpos($sectionName, $schoolName) !== false) {
                return $schoolCode;
            }
        }

        // Default to blank
        return '';
    }
}

function runJob($db, $siteID, $config)
{
    // Set resource limits to prevent runaway processes
    set_time_limit(43200);
    ini_set('memory_limit', '4G');
    
    // Add periodic memory monitoring
    $memoryCheckInterval = 1000;
    $memoryWarningThreshold = 800 * 1024 * 1024;
    
    $cronName = 'Canvas Skyward Integration';

    try {
        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        error_log("=== Canvas Skyward Integration Started ===");
        error_log("Site ID: {$siteID}");

        // Log initial memory usage
        $initialMemory = memory_get_usage(true) / 1024 / 1024;
        $peakMemory = memory_get_peak_usage(true) / 1024 / 1024;
        error_log("Initial memory usage: " . round($initialMemory, 2) . " MB, Peak: " . round($peakMemory, 2) . " MB");

        // Get current school year
        $schoolYearID = getCurrentSchoolYearID($db);
        error_log("School Year ID: {$schoolYearID}");

        // Get Canvas configuration from config parameter
        if (!isset($config->canvas)) {
            throw new Exception("Canvas configuration not found in config parameter for site {$siteID}");
        }
        $canvasConfig = $config->canvas;

        // Initialize Canvas API client
        $canvasApi = new CanvasApiClient(
            $canvasConfig->baseUrl,
            $canvasConfig->clientId,
            $canvasConfig->clientSecret,
            $canvasConfig->refreshToken ?? null,
            false
        );

        // Set manual access token
        if (isset($canvasConfig->accessToken)) {
            $canvasApi->setAccessToken($canvasConfig->accessToken);
        }

        // Authenticate
        if (!$canvasApi->authenticate()) {
            throw new Exception("Failed to authenticate with Canvas API");
        }

        error_log("Canvas: Authentication successful");

        // Get account information following proper Canvas API flow
        $accounts = $canvasApi->get('/api/v1/accounts');
        if ($accounts === false || empty($accounts)) {
            throw new Exception("Failed to retrieve Canvas accounts");
        }
        $accountId = $accounts[0]['id'];
        error_log("Canvas: Using account ID: {$accountId}");

        // Get Canvas terms mapped to current school year from database
        $canvasTermIds = [];

        // Try to get term IDs from database mapping first
        $termMappingStmt = $db->prepare("SELECT grading_period_term_id FROM vendor_canvas_terms WHERE site_id = ? AND school_year_id = ?");
        if ($termMappingStmt) {
            $termMappingStmt->bind_param("ii", $siteID, $schoolYearID);
            $termMappingStmt->execute();
            $result = $termMappingStmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $canvasTermIds[] = (int)$row['grading_period_term_id'];
            }
            $termMappingStmt->close();
            error_log("Canvas: Found " . count($canvasTermIds) . " terms from database mapping for school year {$schoolYearID}");
        }

        // Discover available Canvas terms for validation
        $termsResponse = $canvasApi->get("/api/v1/accounts/{$accountId}/terms");
        if ($termsResponse !== false && isset($termsResponse['enrollment_terms'])) {
            error_log("Canvas: Found " . count($termsResponse['enrollment_terms']) . " total Canvas terms:");
            foreach ($termsResponse['enrollment_terms'] as $term) {
                error_log("Canvas: Term ID {$term['id']}: '{$term['name']}' (Start: {$term['start_at']}, End: {$term['end_at']})");
            }
        }

        // If no database mapping exists, error out or use all terms
        if (empty($canvasTermIds)) {
            error_log("Canvas: WARNING - No Canvas term mapping found in database for school year {$schoolYearID}");
            error_log("Canvas: Please ensure vendor_canvas_terms table has mappings for site {$siteID}, school year {$schoolYearID}");

            // Get all available terms (remove archives/default)
            if ($termsResponse !== false && isset($termsResponse['enrollment_terms'])) {
                foreach ($termsResponse['enrollment_terms'] as $term) {
                    // Only skip archive and default terms
                    if (strpos($term['name'], 'ARCHIVE') === false && $term['id'] != 1) {
                        $canvasTermIds[] = (int)$term['id'];
                        error_log("Canvas: Including term ID {$term['id']}: {$term['name']}");
                    }
                }
            }
            error_log("Canvas: FALLBACK MODE - Using ALL available Canvas terms (" . count($canvasTermIds) . " terms)");
            error_log("Canvas: This may include data from multiple school years");
        }

        error_log("Canvas: Using " . count($canvasTermIds) . " Canvas term IDs for school year {$schoolYearID}: " . implode(', ', $canvasTermIds));

        // Initialize grades processor
        $gradesProcessor = new CanvasGradesProcessor(
            $canvasApi,
            $db,
            $siteID,
            $schoolYearID,
            true,
            $canvasTermIds,
            $uuid
        );

        // Process grades
        $processedRecords = $gradesProcessor->processGrades();
        
        // Check memory usage after processing
        $currentMemory = memory_get_usage(true);
        if ($currentMemory > $memoryWarningThreshold) {
            error_log("Memory usage high after processing: " . round($currentMemory / 1024 / 1024, 2) . " MB");
            // Force garbage collection
            gc_collect_cycles();
        }

        try {
            $storage = new StorageClient(['projectId' => "abre-production"]);
            $bucketName = "abre-canvas-skyward";
            $bucket = $storage->bucket($bucketName);
            $currentDate = date('Ymd');

            // Upload grades as CSV to GCS
            $gradesData = $gradesProcessor->getGradesData();
            if (!empty($gradesData)) {
                $memoryBefore = memory_get_usage(true) / 1024 / 1024;
                _uploadGradesCSVToGCS($gradesData, $bucket, $currentDate, $siteID);
                $memoryAfter = memory_get_usage(true) / 1024 / 1024;
                $memoryDelta = $memoryAfter - $memoryBefore;
                error_log("Uploaded grades CSV: " . count($gradesData) . " records");
                error_log("Memory before/after CSV upload: " . round($memoryBefore, 2) . "MB / " . round($memoryAfter, 2) . "MB (Δ" . round($memoryDelta, 2) . "MB)");
                
                // Force garbage collection after CSV upload
                gc_collect_cycles();
            }

            // Upload assignments in batches to prevent memory exhaustion
            $totalAssignmentsUploaded = 0;
            $assignmentsBatches = $gradesProcessor->getAssignmentsDataForGcsInBatches();
            foreach ($assignmentsBatches as $batchIndex => $assignmentsBatch) {
                if (!empty($assignmentsBatch)) {
                    $batchMemoryBefore = memory_get_usage(true) / 1024 / 1024;
                    $batchFileName = 'assignments_batch_' . ($batchIndex + 1);
                    _uploadToGCS($assignmentsBatch, $batchFileName, $bucket, $currentDate, $siteID);
                    $totalAssignmentsUploaded += count($assignmentsBatch);

                    $batchMemoryAfter = memory_get_usage(true) / 1024 / 1024;
                    $memoryDelta = $batchMemoryAfter - $batchMemoryBefore;
                    error_log("Uploaded assignments batch " . ($batchIndex + 1) . ": " . count($assignmentsBatch) . " records");
                    error_log("Memory before/after batch: " . round($batchMemoryBefore, 2) . "MB / " . round($batchMemoryAfter, 2) . "MB (Δ" . round($memoryDelta, 2) . "MB)");

                    // Force garbage collection after each batch
                    gc_collect_cycles();
                }
            }

            // Clear the data arrays after successful upload
            $gradesProcessor->clearGradesDataAfterBatching();
            $gradesProcessor->clearAssignmentsDataAfterBatching();

            error_log("GCS upload successful: " . count($gradesData) . " grades as CSV and " . $totalAssignmentsUploaded . " assignments uploaded in batches");
        } catch (Exception $e) {
            error_log('GCS upload failed: ' . $e->getMessage());
            // Get remaining data for logging even if GCS upload failed
            $remainingGrades = $gradesProcessor->getGradesData();
            $remainingAssignments = $gradesProcessor->getAssignmentsDataForGcs();
            error_log("Remaining data: " . count($remainingGrades) . " grades and " . count($remainingAssignments) . " assignments not uploaded");
        }

        // Get final counts for logging (regardless of GCS upload success)
        $finalAssignmentsCount = count($gradesProcessor->getAssignmentsDataForGcs());

        error_log("Canvas: Processed {$processedRecords} grade records");
        error_log("Canvas: Processed {$finalAssignmentsCount} assignment records");

        // === FINAL STEPS: Merge and Insert Grades ===
        error_log("=== Starting Final Steps: Merge and Insert Grades ===");
        
        try {
            // Step 1: Download the new Canvas grades CSV from GCS
            $newGradesPath = "abre_grades_canvas_api.csv";
            $newGradesCSV = _downloadCSVFromGCS($bucket, $newGradesPath);
            
            if (empty($newGradesCSV)) {
                error_log('Warning: Could not download new Canvas grades CSV, skipping final steps');
            } else {
                // Step 2: Download the original Abre grades 2.0 CSV from GCS
                // Note: This assumes the original grades are stored in a specific location
                // You may need to adjust the path based on your actual file structure
                $originalGradesPath = "Abre_Grades_2.0.csv";
                $originalGradesCSV = _downloadCSVFromGCS($bucket, $originalGradesPath);
                
                if (empty($originalGradesCSV)) {
                    error_log('Warning: Could not download original Abre grades CSV, using new grades as final');
                    $finalGradesCSV = $newGradesCSV;
                } else {
                    // Step 3: Merge the new Canvas grades with original Abre grades
                    $finalGradesCSV = _mergeGradesCSV($newGradesCSV, $originalGradesCSV);
                    
                    if (empty($finalGradesCSV)) {
                        error_log('Warning: Could not merge grades CSVs, using new grades as final');
                        $finalGradesCSV = $newGradesCSV;
                    }
                }
                
                // Step 4: Upload the merged CSV as final version to GCS
                if (!empty($finalGradesCSV)) {
                    $uploadSuccess = _uploadFinalGradesCSVToGCS($finalGradesCSV, $bucket, $currentDate, $siteID);
                    
                    if ($uploadSuccess) {
                        error_log('Successfully completed: final grades CSV uploaded to GCS');
                    } else {
                        error_log('Warning: Failed to upload final grades CSV to GCS');
                    }
                } else {
                    error_log('Warning: No final grades CSV content available for upload');
                }
            }
        } catch (Exception $e) {
            error_log('Error in final steps: ' . $e->getMessage());
            // Don't fail the entire job if final steps fail
        }

        // Log final memory usage
        $finalMemory = memory_get_usage(true) / 1024 / 1024;
        $finalPeakMemory = memory_get_peak_usage(true) / 1024 / 1024;
        error_log("Final memory usage: " . round($finalMemory, 2) . " MB, Peak: " . round($finalPeakMemory, 2) . " MB");

        error_log("=== Canvas Skyward Integration Complete ===");

        $status = CRON_SUCCESS;
        $details = [
            'gradesProcessed' => $processedRecords,
            'schoolYearId'    => $schoolYearID
        ];

    } catch (Exception $ex) {
        $error = $ex->getMessage();
        error_log("Canvas Skyward Integration Error: " . $error);

        $status = CRON_FAILURE;
        $details = ['error' => $error];
    }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
